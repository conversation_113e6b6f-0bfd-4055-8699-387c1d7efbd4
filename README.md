# Dashboard PIX - Projeto Global WebApp

Este é um dashboard para gerenciamento e monitoramento de transações PIX. O projeto foi desenvolvido usando Vue 3, TypeScript e ApexCharts para visualização de dados.

## Estrutura do Projeto

O projeto segue uma arquitetura de componentes modular:

- **Views**

  - `DashHome.vue` - Página principal do dashboard

- **Componentes**
  - `Sidebar.vue` - Menu lateral responsivo
  - `StatCard.vue` - Cards de estatísticas
  - `TransactionsChart.vue` - Gráfico de transações usando ApexCharts
  - `QrCodeGenerator.vue` - Componente para geração de QR Code PIX
  - `TransactionsTable.vue` - Tabela de transações com filtragem e paginação

## Recursos Implementados

- **Dashboard Completo** - Visão geral das transações PIX
- **Sidebar Responsivo** - Menu lateral que se adapta a dispositivos móveis com as seguintes características:
  - Inicia fechado em dispositivos móveis e aberto em desktops
  - Possui overlay quando aberto em dispositivos móveis
  - Botão flutuante para abrir o menu quando fechado em móveis
  - Animações suaves de transição
  - Ajuste automático baseado no tamanho da tela
- **Gráficos Interativos** - Visualização de dados usando ApexCharts
- **Tabela de Transações** - Listagem com filtragem e paginação
- **QR Code PIX** - Geração de QR Codes para pagamentos PIX
- **Design Responsivo** - Interface adaptada para todos os tamanhos de tela

## Tecnologias Utilizadas

- Vue 3
- TypeScript
- ApexCharts
- Tailwind CSS (para estilização)

## Próximos Passos

1. Integração com API real para busca de transações
2. Implementação da geração real de QR Codes PIX
3. Adição de mais filtros e funcionalidades de pesquisa
4. Implementação de autenticação e autorização
5. Melhorias de performance e otimizações

## Como Executar

1. Clone o repositório
2. Instale as dependências:
   ```
   npm install
   ```
3. Execute o projeto em desenvolvimento:
   ```
   npm run dev
   ```

## Considerações sobre Design

O dashboard foi projetado seguindo princípios de UI/UX modernos, com foco em:

- Clareza na apresentação dos dados
- Acessibilidade
- Responsividade
- Consistência visual

## Contribuições

Contribuições são bem-vindas! Sinta-se à vontade para abrir issues ou enviar pull requests com melhorias.
