<?php

use App\Http\Controllers\API\V1\AuthController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\V1\PixAccountController;
use App\Http\Controllers\API\V1\PixTransactionController;
use App\Http\Controllers\WebhookController;

// Route::post('/global/receipt/webhook', [WebhookController::class, 'webhook']);

Route::prefix('v1')->group(function () {
    Route::prefix('auth')->group(function () {
        Route::post('/register', [AuthController::class, 'register']);
        Route::post('/login', [AuthController::class, 'login']);
    });

    Route::middleware('auth:sanctum')->group(function () {
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::get('/me', [AuthController::class, 'me']);
    });

    // Rotas protegidas por API Key
    Route::middleware('api.key')->group(function () {
        // Rotas de Contas PIX
        Route::prefix('pix-accounts')->group(function () {
            Route::get('/', [PixAccountController::class, 'index']);
            Route::get('/webhook', [PixAccountController::class, 'getWebhook']);
            Route::patch('/webhook', [PixAccountController::class, 'storeWebhook']);
            Route::get('/{id}', [PixAccountController::class, 'show']);
            Route::get('/{id}/balance', [PixAccountController::class, 'balance']);
        });

        // Rotas de Transações PIX
        Route::prefix('pix-transactions')->group(function () {
            Route::post('/qrcode', [PixTransactionController::class, 'deposit']);
            Route::post('/withdraw', [PixTransactionController::class, 'withdraw']);
            Route::get('/', [PixTransactionController::class, 'index']);
            Route::get('/{id}', [PixTransactionController::class, 'show']);
        });

    });

    Route::get('/health', function () {
        return response()->json(['status' => 'ok']);
    })->name('health');
});

