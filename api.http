### Login
POST http://localhost:8000/api/v1/auth/login
Content-Type: application/json
Accept: application/json

{
    "email": "<EMAIL>",
    "password": "@Naits071"
}


### Register
POST http://localhost:8000/api/v1/auth/register
Content-Type: application/json
Accept: application/json

{
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "email": "<EMAIL>",
  "password": "@Naits071",
  "password_confirmation": "@Naits071",
  "phone": "11999999999",
  "document": "12345678900",
  "agreement_terms": true
}
### Me (requer token do login)
GET http://localhost:8000/api/v1/me
Content-Type: application/json
Accept: application/json
Authorization: Bearer "1|jESVpaPkXkZKLAqzTqdnZWExBgP8gkk4RZP0BaWqf482a29b"

