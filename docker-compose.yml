services:
  postgres:
    image: postgres:latest
    container_name: global_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DB_DATABASE}
      POSTGRES_USER: ${DB_USERNAME}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - global_network

  redis:
    image: redis:7.4.1-alpine
    container_name: global_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: ["redis-server", "--requirepass", ""]
    networks:
      - global_network

networks:
  global_network:

volumes:
  postgres_data:
