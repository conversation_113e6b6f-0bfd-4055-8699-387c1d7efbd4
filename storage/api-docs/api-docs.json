{"openapi": "3.0.0", "info": {"title": "API Global Core", "description": "Documentação completa da API Global Core, incluindo autenticação, gerenciamento de usuários, contas Pix e transações.", "contact": {"name": "Equipe de Desenvolvimento", "url": "https://globalcore.com", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0.0"}, "servers": [{"url": "http://localhost:8000", "description": "Servidor principal"}], "paths": {"/api/v1/auth/register": {"post": {"tags": ["Cadastro"], "summary": "Registrar novo usuário com KYC", "description": "Registra um novo usuário (PF ou PJ) e faz upload dos documentos para KYC em uma única requisição", "operationId": "e6820a70bd152640eefa0af0016b3d3b", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"required": ["first_name", "last_name", "email", "password", "password_confirmation", "phone", "document", "document_type", "agreement_terms", "selfie"], "properties": {"first_name": {"type": "string", "example": "<PERSON>"}, "last_name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "password": {"type": "string", "format": "password", "example": "senha123"}, "password_confirmation": {"type": "string", "format": "password", "example": "senha123"}, "phone": {"type": "string", "example": "11999999999"}, "document": {"description": "CPF do responsável legal para PJ ou CPF para PF", "type": "string", "example": "12345678900"}, "document_type": {"type": "string", "enum": ["cpf", "cnpj"], "example": "cpf"}, "agreement_terms": {"type": "boolean", "example": true}, "company_legal_name": {"description": "Obrigatório para PJ", "type": "string", "example": "Empresa XYZ Ltda"}, "company_trading_name": {"description": "Obrigatório para PJ", "type": "string", "example": "Empresa XYZ"}, "company_document": {"description": "CNPJ da empresa - Obrigatório para PJ", "type": "string", "example": "12345678901234"}, "document_front": {"description": "Foto da frente do documento (RG/CNH) - Obrigatório para PF, JPG, PNG ou PDF, máximo 5MB", "type": "string", "format": "binary"}, "document_back": {"description": "Foto do verso do documento (RG/CNH) - Obrigatório para PF, JPG, PNG ou PDF, máximo 5MB", "type": "string", "format": "binary"}, "company_contract": {"description": "Contrato Social - Obrigatório para PJ, JPG, PNG ou PDF, máximo 5MB", "type": "string", "format": "binary"}, "company_statute": {"description": "Estatuto Social - Obrigatório para PJ, JPG, PNG ou PDF, máximo 5MB", "type": "string", "format": "binary"}, "company_proxy": {"description": "Procuração - Obrigatório para PJ, JPG, PNG ou PDF, máximo 5MB", "type": "string", "format": "binary"}, "selfie": {"description": "Selfie segurando o documento - Obrigatório para todos, JPG ou PNG, máximo 5MB", "type": "string", "format": "binary"}}, "type": "object"}}}}, "responses": {"201": {"description": "Usuário registrado com sucesso", "content": {"application/json": {"schema": {"properties": {"user": {"properties": {"id": {"type": "integer", "example": 1}, "first_name": {"type": "string", "example": "<PERSON>"}, "last_name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "phone": {"type": "string", "example": "11999999999"}, "document": {"type": "string", "example": "12345678900"}, "document_type": {"type": "string", "example": "cpf"}, "company_legal_name": {"type": "string", "example": "Empresa XYZ Ltda"}, "company_trading_name": {"type": "string", "example": "Empresa XYZ"}, "company_document": {"type": "string", "example": "12345678901234"}, "kyc_status": {"type": "string", "example": "under_review"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}, "credentials": {"properties": {"api_key": {"type": "string", "example": "pk_live_123456..."}, "api_secret": {"type": "string", "example": "sk_live_123456..."}}, "type": "object"}, "kyc_status": {"type": "string", "example": "under_review"}}, "type": "object"}}}}, "422": {"description": "Erro de validação", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Os dados fornecidos são inválidos."}, "errors": {"properties": {"document_front": {"type": "array", "items": {"type": "string", "example": "A foto da frente do documento é obrigatória"}}}, "type": "object"}}, "type": "object"}}}}, "500": {"description": "Erro ao registrar usuário", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Erro ao processar upload dos documentos"}}, "type": "object"}}}}}}}, "/api/v1/pix-accounts/{id}/balance": {"get": {"tags": ["Conta"], "summary": "Consultar saldo da conta PIX", "operationId": "3e9913478c5c60bbb2d12d5a4dd773c9", "parameters": [{"name": "id", "in": "path", "description": "ID da conta PIX", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Saldo retornado com sucesso", "content": {"application/json": {"schema": {"properties": {"balance": {"type": "number", "format": "float"}}, "type": "object"}}}}, "404": {"description": "Conta PIX não encontrada"}, "500": {"description": "Erro ao consultar saldo"}}, "security": [{"apiKey": [], "apiSecret": []}]}}, "/api/v1/pix-accounts/webhook": {"get": {"tags": ["Webhooks"], "summary": "Consultar webhook configurado", "description": "Retorna a URL do webhook atualmente configurada para a conta", "operationId": "e0ac1e5a8981b6467bd5bffd1e7e0d77", "responses": {"200": {"description": "Webhook retornado com sucesso", "content": {"application/json": {"schema": {"properties": {"webhook_url": {"description": "URL do webhook configurada (null se não configurada)", "type": "string", "format": "url", "example": "https://meusite.com/webhook"}}, "type": "object"}}}}, "401": {"description": "Não autorizado", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Unauthenticated"}}, "type": "object"}}}}, "500": {"description": "Erro interno do servidor", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Erro interno do servidor"}}, "type": "object"}}}}}, "security": [{"apiKey": [], "apiSecret": []}]}, "patch": {"tags": ["Webhooks"], "summary": "<PERSON><PERSON><PERSON><PERSON> ou at<PERSON><PERSON><PERSON> webhook", "description": "Configura a URL do webhook para receber notificações de transações e mudanças de status KYC", "operationId": "4a4b5bcfb1f17b82003f45945d0cc202", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["webhook_url"], "properties": {"webhook_url": {"description": "URL do endpoint que receberá as notificações webhook", "type": "string", "format": "url", "example": "https://meusite.com/webhook"}}, "type": "object"}}}}, "responses": {"200": {"description": "Webhook configurado com sucesso", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Webhook atualizado com sucesso"}}, "type": "object"}}}}, "422": {"description": "Erro de validação", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "O campo webhook_url é obrigatório."}, "errors": {"properties": {"webhook_url": {"type": "array", "items": {"type": "string", "example": "O campo webhook_url deve ser uma URL válida."}}}, "type": "object"}}, "type": "object"}}}}, "500": {"description": "Erro interno do servidor", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Erro ao criar webhook: ..."}}, "type": "object"}}}}}, "security": [{"apiKey": [], "apiSecret": []}]}}, "/api/v1/pix-transactions/deposit": {"post": {"tags": ["Transações"], "summary": "Realizar depósito PIX", "operationId": "96c0cc6c56e702fae7e9b50419f6c7dd", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["pix_account_id", "amount", "description"], "properties": {"pix_account_id": {"description": "ID da conta PIX de destino", "type": "integer"}, "amount": {"description": "Valor do depósito", "type": "number", "format": "float"}, "description": {"type": "string", "maxLength": 1000}, "qr_code": {"description": "QR Code do PIX (opcional)", "type": "string"}}, "type": "object"}}}}, "responses": {"201": {"description": "Depósito PIX iniciado com sucesso", "content": {"application/json": {"schema": {"properties": {"transaction": {"type": "object"}, "qr_code": {"type": "string"}, "pix_key": {"type": "string"}}, "type": "object"}}}}, "404": {"description": "Conta PIX não encontrada"}, "500": {"description": "Erro ao iniciar depósito PIX"}}, "security": [{"apiKey": [], "apiSecret": []}]}}, "/api/v1/pix-transactions/withdraw": {"post": {"tags": ["Transações"], "summary": "Realizar saque P<PERSON>", "operationId": "e85c9c983b764d7c2b8f9370dbc19b30", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["pix_account_id", "amount", "pix_key"], "properties": {"pix_account_id": {"description": "ID da conta PIX de origem", "type": "integer"}, "amount": {"description": "Valor do saque", "type": "number", "format": "float"}, "description": {"type": "string", "maxLength": 1000}, "pix_key": {"description": "Chave PIX do destinatário", "type": "string"}}, "type": "object"}}}}, "responses": {"201": {"description": "Saque PIX iniciado com sucesso", "content": {"application/json": {"schema": {"properties": {"transaction": {"type": "object"}, "status": {"type": "string"}}, "type": "object"}}}}, "404": {"description": "Conta PIX não encontrada"}, "400": {"description": "<PERSON><PERSON> insuficiente"}, "500": {"description": "Erro ao iniciar saque P<PERSON>"}}, "security": [{"apiKey": [], "apiSecret": []}]}}, "/api/v1/pix-transactions": {"get": {"tags": ["Transações"], "summary": "Listar transações PIX da conta", "description": "Lista todas as transações PIX da conta autenticada com filtros opcionais", "operationId": "82c5a072e70d85ab170a073f0cd5dba2", "parameters": [{"name": "status", "in": "query", "description": "Filtrar por status (pending, pending_payment, received, completed, failed, cancelled)", "required": false, "schema": {"type": "string", "enum": ["pending", "pending_payment", "received", "completed", "failed", "cancelled"]}}, {"name": "type", "in": "query", "description": "Filtrar por tipo (deposit, withdraw)", "required": false, "schema": {"type": "string", "enum": ["deposit", "withdraw"]}}, {"name": "date_from", "in": "query", "description": "Data inicial (formato: Y-m-d)", "required": false, "schema": {"type": "string", "format": "date", "example": "2024-01-01"}}, {"name": "date_to", "in": "query", "description": "Data final (formato: Y-m-d)", "required": false, "schema": {"type": "string", "format": "date", "example": "2024-12-31"}}, {"name": "page", "in": "query", "description": "Número da página para paginação", "required": false, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "Lista de transações retornada com sucesso", "content": {"application/json": {"schema": {"properties": {"data": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "transaction_id_external": {"type": "string", "example": "TXN123456"}, "order_id": {"type": "string", "example": "ORD789"}, "type": {"type": "string", "example": "deposit"}, "amount": {"type": "number", "format": "float", "example": 100.5}, "status": {"type": "string", "example": "completed"}, "description": {"type": "string", "example": "Depósito PIX"}, "pix_key": {"type": "string", "example": "<EMAIL>"}, "qr_code": {"type": "string", "example": "PIX_QR_CODE_STRING"}, "created_at": {"type": "string", "format": "date-time", "example": "2024-01-01T12:00:00Z"}, "processed_at": {"type": "string", "format": "date-time", "example": "2024-01-01T12:15:00Z"}}, "type": "object"}}, "current_page": {"type": "integer", "example": 1}, "last_page": {"type": "integer", "example": 5}, "per_page": {"type": "integer", "example": 20}, "total": {"type": "integer", "example": 95}}, "type": "object"}}}}, "401": {"description": "Não autorizado", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Unauthenticated"}}, "type": "object"}}}}, "500": {"description": "Erro interno do servidor", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Erro ao listar transações: ..."}}, "type": "object"}}}}}, "security": [{"apiKey": [], "apiSecret": []}]}}}, "tags": [{"name": "Cadastro", "description": "Cadastro"}, {"name": "Conta", "description": "Conta"}, {"name": "Webhooks", "description": "Webhooks"}, {"name": "Transações", "description": "Transações"}], "components": {"securitySchemes": {"sanctum": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Insira o token no formato (Bearer <token>)", "name": "Authorization", "in": "header"}}}}