<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Adapters\ApiPixAdapter;
use Illuminate\Support\Facades\Log;

class ApiPixAdapterTest extends TestCase
{
  private ApiPixAdapter $adapter;

  protected function setUp(): void
  {
    parent::setUp();

    // Força o modo mock para os testes
    $this->adapter = new ApiPixAdapter();
    $this->adapter->setMockMode(true);
  }

  public function test_can_generate_mock_token()
  {
    $token = $this->adapter->getToken();

    $this->assertNotEmpty($token);
    $this->assertStringContainsString('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9', $token);
    $this->assertTrue($this->adapter->isTokenValid());
  }

  public function test_can_create_mock_pix_deposit()
  {
    $dataPayment = [
      "calendario" => [
        "expiracao" => 7200
      ],
      "devedor" => [
        "cnpj" => "12345678000195",
        "nome" => "Empresa Teste LTDA"
      ],
      "valor" => [
        "original" => "100.50",
        "modalidadeAlteracao" => 1
      ],
      "chave" => "<EMAIL>",
      "solicitacaoPagador" => "Pagamento de teste"
    ];

    $response = $this->adapter->createPixDeposit($dataPayment);

    $this->assertIsArray($response);
    $this->assertEquals('ATIVA', $response['status']);
    $this->assertArrayHasKey('txid', $response);
    $this->assertArrayHasKey('pixCopiaECola', $response);
    $this->assertArrayHasKey('calendario', $response);
    $this->assertArrayHasKey('location', $response);

    // Verifica se os dados do pagador foram preservados
    $this->assertEquals($dataPayment['devedor']['cnpj'], $response['devedor']['cnpj']);
    $this->assertEquals($dataPayment['devedor']['nome'], $response['devedor']['nome']);
    $this->assertEquals($dataPayment['valor']['original'], $response['valor']['original']);
    $this->assertEquals($dataPayment['chave'], $response['chave']);
    $this->assertEquals($dataPayment['solicitacaoPagador'], $response['solicitacaoPagador']);
  }

  public function test_can_get_mock_pix_transaction()
  {
    $transactionId = 'test-transaction-123';

    $response = $this->adapter->getPixTransaction($transactionId);

    $this->assertIsArray($response);
    $this->assertEquals($transactionId, $response['txid']);
    $this->assertArrayHasKey('status', $response);
    $this->assertArrayHasKey('calendario', $response);
    $this->assertArrayHasKey('devedor', $response);
    $this->assertArrayHasKey('valor', $response);
    $this->assertContains($response['status'], ['ATIVA', 'CONCLUIDA', 'REMOVIDA_PELO_USUARIO_RECEBEDOR']);
  }

  public function test_mock_transaction_with_completed_status_has_pix_info()
  {
    // Usa um ID que sabemos que gerará status CONCLUIDA
    $transactionId = 'completed-transaction';

    $response = $this->adapter->getPixTransaction($transactionId);

    if ($response['status'] === 'CONCLUIDA') {
      $this->assertArrayHasKey('pix', $response);
      $this->assertIsArray($response['pix']);
      $this->assertNotEmpty($response['pix']);

      $pixInfo = $response['pix'][0];
      $this->assertArrayHasKey('endToEndId', $pixInfo);
      $this->assertArrayHasKey('txid', $pixInfo);
      $this->assertArrayHasKey('valor', $pixInfo);
      $this->assertArrayHasKey('horario', $pixInfo);
    }
  }

  public function test_mock_mode_status()
  {
    $this->assertTrue($this->adapter->isMockMode());

    $this->adapter->setMockMode(false);
    $this->assertFalse($this->adapter->isMockMode());

    $this->adapter->setMockMode(true);
    $this->assertTrue($this->adapter->isMockMode());
  }

  public function test_mock_pix_copy_paste_is_generated()
  {
    $dataPayment = [
      "valor" => [
        "original" => "50.00"
      ],
      "chave" => "<EMAIL>"
    ];

    $response = $this->adapter->createPixDeposit($dataPayment);

    $this->assertArrayHasKey('pixCopiaECola', $response);
    $this->assertNotEmpty($response['pixCopiaECola']);
    $this->assertStringContainsString('00020126', $response['pixCopiaECola']); // Início padrão PIX
    $this->assertStringContainsString('br.gov.bcb.pix', $response['pixCopiaECola']); // Identificador PIX
  }

  public function test_mock_response_structure_matches_real_api()
  {
    $dataPayment = [
      "calendario" => ["expiracao" => 3600],
      "devedor" => ["cnpj" => "12345678000195", "nome" => "Teste"],
      "valor" => ["original" => "25.75", "modalidadeAlteracao" => 1],
      "chave" => "<EMAIL>",
      "solicitacaoPagador" => "Teste de estrutura"
    ];

    $response = $this->adapter->createPixDeposit($dataPayment);

    // Verifica estrutura completa baseada na resposta real fornecida
    $expectedKeys = [
      'calendario',
      'status',
      'txid',
      'revisao',
      'location',
      'devedor',
      'loc',
      'valor',
      'chave',
      'solicitacaoPagador',
      'pixCopiaECola'
    ];

    foreach ($expectedKeys as $key) {
      $this->assertArrayHasKey($key, $response, "Chave '$key' não encontrada na resposta");
    }

    // Verifica estrutura do calendário
    $this->assertArrayHasKey('criacao', $response['calendario']);
    $this->assertArrayHasKey('expiracao', $response['calendario']);

    // Verifica estrutura do devedor
    $this->assertArrayHasKey('cnpj', $response['devedor']);
    $this->assertArrayHasKey('nome', $response['devedor']);

    // Verifica estrutura do loc
    $this->assertArrayHasKey('id', $response['loc']);
    $this->assertArrayHasKey('location', $response['loc']);
    $this->assertArrayHasKey('tipoCob', $response['loc']);
    $this->assertArrayHasKey('criacao', $response['loc']);

    // Verifica estrutura do valor
    $this->assertArrayHasKey('original', $response['valor']);
    $this->assertArrayHasKey('modalidadeAlteracao', $response['valor']);
    $this->assertArrayHasKey('retirada', $response['valor']);
    $this->assertNull($response['valor']['retirada']);
  }
}
