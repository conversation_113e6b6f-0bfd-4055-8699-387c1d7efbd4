<?php

namespace Tests\Feature\API\V1;

use Tests\TestCase;
use App\Models\User;
use App\Models\WebhookEvent;
use Illuminate\Foundation\Testing\RefreshDatabase;

class WebhookTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_create_and_update_webhook()
    {
        // Criar usuário sem webhook inicialmente
        $user = User::factory()->create(['webhook_url' => null]);

        // Verificar que não tem webhook inicialmente
        $this->assertNull($user->webhook_url);

        // Atualizar com webhook no usuário
        $user->update(['webhook_url' => 'https://meusite.com/webhook']);

        $this->assertEquals('https://meusite.com/webhook', $user->fresh()->webhook_url);

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'webhook_url' => 'https://meusite.com/webhook'
        ]);

        // Atualizar webhook novamente
        $user->update(['webhook_url' => 'https://novosite.com/webhook']);

        $this->assertEquals('https://novosite.com/webhook', $user->fresh()->webhook_url);
    }

    public function test_can_create_webhook_for_pf_user()
    {
        // Criar usuário PF
        $user = User::create([
            'first_name' => 'João',
            'last_name' => 'Silva',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'phone' => '11987654321',
            'document_type' => 'cpf',
            'document' => '12345678901',
            'agreement_terms' => true,
            'kyc_status' => 'verified',
        ]);

        // Configurar webhook
        $webhookUrl = 'https://joao.com/api/webhook';
        $user->update(['webhook_url' => $webhookUrl]);

        // Verificações
        $this->assertTrue($user->isPF());
        $this->assertEquals($webhookUrl, $user->webhook_url);

        $this->assertDatabaseHas('users', [
            'document_type' => 'cpf',
            'webhook_url' => $webhookUrl
        ]);
    }

    public function test_can_create_webhook_for_pj_user()
    {
        // Criar usuário PJ
        $user = User::create([
            'first_name' => 'Maria',
            'last_name' => 'Santos',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'phone' => '11987654321',
            'document_type' => 'cnpj',
            'document' => '98765432100',
            'document_company' => '12345678000199',
            'company_legal_name' => 'Empresa Exemplo Ltda',
            'company_trading_name' => 'Empresa Exemplo',
            'agreement_terms' => true,
            'kyc_status' => 'verified',
        ]);

        // Configurar webhook empresarial
        $webhookUrl = 'https://empresa.com/api/webhooks/pix';
        $user->update(['webhook_url' => $webhookUrl]);

        // Verificações
        $this->assertTrue($user->isPJ());
        $this->assertEquals($webhookUrl, $user->webhook_url);

        $this->assertDatabaseHas('users', [
            'document_type' => 'cnpj',
            'company_legal_name' => 'Empresa Exemplo Ltda',
            'webhook_url' => $webhookUrl
        ]);
    }

    public function test_can_remove_webhook_url()
    {
        // Criar usuário com webhook
        $user = User::factory()->create([
            'webhook_url' => 'https://antigo.com/webhook'
        ]);

        // Verificar que tem webhook
        $this->assertNotNull($user->webhook_url);
        $this->assertEquals('https://antigo.com/webhook', $user->webhook_url);

        // Remover webhook
        $user->update(['webhook_url' => null]);

        // Verificar que foi removido
        $this->assertNull($user->fresh()->webhook_url);

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'webhook_url' => null
        ]);
    }

    public function test_webhook_url_validation()
    {
        $user = User::factory()->create();

        // URLs válidas
        $validUrls = [
            'https://example.com/webhook',
            'https://api.empresa.com/v1/webhooks/pix',
            'https://subdomain.domain.com.br/webhook-handler',
        ];

        foreach ($validUrls as $url) {
            $user->update(['webhook_url' => $url]);
            $this->assertEquals($url, $user->fresh()->webhook_url);
        }
    }

    public function test_can_create_webhook_events()
    {
        // Criar usuário com webhook
        $user = User::factory()->create([
            'webhook_url' => 'https://example.com/webhook'
        ]);

        // Criar evento de webhook
        $webhookEvent = WebhookEvent::create([
            'user_id' => $user->id,
            'event_type' => 'pix.deposit.completed',
            'payload' => [
                'transaction_id' => 'txn_123456',
                'amount' => 10000,
                'status' => 'completed',
                'timestamp' => now()->toISOString()
            ],
            'status' => 'pending',
            'attempts' => 0,
        ]);

        // Verificações
        $this->assertInstanceOf(WebhookEvent::class, $webhookEvent);
        $this->assertEquals($user->id, $webhookEvent->user_id);
        $this->assertEquals('pix.deposit.completed', $webhookEvent->event_type);
        $this->assertEquals('pending', $webhookEvent->status);
        $this->assertEquals(0, $webhookEvent->attempts);

        $this->assertDatabaseHas('webhook_events', [
            'user_id' => $user->id,
            'event_type' => 'pix.deposit.completed',
            'status' => 'pending'
        ]);
    }

    public function test_webhook_event_for_different_types()
    {
        $user = User::factory()->create([
            'webhook_url' => 'https://test.com/webhook'
        ]);

        // Diferentes tipos de eventos
        $eventTypes = [
            'pix.deposit.completed',
            'pix.deposit.failed',
            'pix.withdraw.completed',
            'pix.withdraw.failed',
            'kyc.verification.completed',
            'kyc.verification.failed'
        ];

        foreach ($eventTypes as $eventType) {
            $event = WebhookEvent::create([
                'user_id' => $user->id,
                'event_type' => $eventType,
                'payload' => ['event' => $eventType, 'timestamp' => now()],
                'status' => 'pending',
                'attempts' => 0,
            ]);

            $this->assertEquals($eventType, $event->event_type);
            $this->assertDatabaseHas('webhook_events', [
                'user_id' => $user->id,
                'event_type' => $eventType
            ]);
        }

        // Verificar que todos os eventos foram criados
        $totalEvents = WebhookEvent::where('user_id', $user->id)->count();
        $this->assertEquals(count($eventTypes), $totalEvents);
    }
}
