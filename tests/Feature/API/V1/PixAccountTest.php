<?php

namespace Tests\Feature\API\V1;

use Tests\TestCase;
use App\Models\User;
use App\Models\PixAccount;
use App\Models\ApiKey;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PixAccountTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_create_pix_account_with_credentials()
    {
        // 1. Criar usuário
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'kyc_status' => 'verified'
        ]);

        // 2. Criar conta PIX
        $pixAccountData = [
            'user_id' => $user->id,
            'bank_code' => '001',
            'account_number' => '12345-6',
            'account_type' => 'corrente',
            'pix_key' => '<EMAIL>',
            'pix_key_type' => 'email',
            'balance' => 0,
            'is_active' => true,
        ];

        $pixAccount = PixAccount::create($pixAccountData);

        // 3. Criar API Key
        $apiKeyData = [
            'user_id' => $user->id,
            'key' => 'test_key_123',
            'secret' => 'test_secret_456',
            'description' => 'Chave de API principal',
            'status' => 'active',
        ];

        $apiKey = ApiKey::create($apiKeyData);

        // Verificações
        $this->assertInstanceOf(PixAccount::class, $pixAccount);
        $this->assertInstanceOf(ApiKey::class, $apiKey);
        $this->assertEquals($user->id, $pixAccount->user_id);
        $this->assertEquals($user->id, $apiKey->user_id);
        $this->assertEquals('<EMAIL>', $pixAccount->pix_key);
        $this->assertEquals('test_key_123', $apiKey->key);

        $this->assertDatabaseHas('pix_accounts', [
            'user_id' => $user->id,
            'pix_key' => '<EMAIL>',
            'is_active' => true
        ]);

        $this->assertDatabaseHas('api_keys', [
            'user_id' => $user->id,
            'key' => 'test_key_123',
            'status' => 'active'
        ]);
    }

    public function test_can_check_account_balance()
    {
        // Setup
        $user = User::factory()->create();
        $pixAccount = PixAccount::factory()->create([
            'user_id' => $user->id,
            'balance' => 25000 // R$ 250,00 em centavos
        ]);

        // Verificar saldo inicial
        $this->assertEquals(25000, $pixAccount->balance);
        $this->assertEquals(250.00, $pixAccount->balance / 100); // Conversão para reais

        // Simular uma atualização de saldo
        $pixAccount->update(['balance' => 50000]); // R$ 500,00

        // Verificar novo saldo
        $updatedAccount = $pixAccount->fresh();
        $this->assertEquals(50000, $updatedAccount->balance);
        $this->assertEquals(500.00, $updatedAccount->balance / 100);

        // Verificar no banco de dados
        $this->assertDatabaseHas('pix_accounts', [
            'id' => $pixAccount->id,
            'user_id' => $user->id,
            'balance' => 50000
        ]);
    }

    public function test_can_create_pf_account_with_pix_and_credentials()
    {
        // 1. Criar usuário PF
        $user = User::create([
            'first_name' => 'João',
            'last_name' => 'Silva Santos',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'phone' => '***********',
            'document_type' => 'cpf',
            'document' => '***********',
            'agreement_terms' => true,
            'kyc_status' => 'verified',
        ]);

        // 2. Criar conta PIX para PF
        $pixAccount = PixAccount::create([
            'user_id' => $user->id,
            'bank_code' => '001',
            'account_number' => '11111-1',
            'account_type' => 'corrente',
            'pix_key' => '<EMAIL>',
            'pix_key_type' => 'email',
            'balance' => 0,
            'is_active' => true,
        ]);

        // 3. Criar credenciais de API
        $apiKey = ApiKey::create([
            'user_id' => $user->id,
            'key' => 'pf_key_123',
            'secret' => 'pf_secret_456',
            'description' => 'API Key PF João',
            'status' => 'active',
        ]);

        // Verificações
        $this->assertTrue($user->isPF());
        $this->assertFalse($user->isPJ());
        $this->assertEquals('<EMAIL>', $pixAccount->pix_key);
        $this->assertEquals('email', $pixAccount->pix_key_type);
        $this->assertEquals('active', $apiKey->status);

        $this->assertDatabaseHas('users', [
            'document_type' => 'cpf',
            'document' => '***********'
        ]);
    }

    public function test_can_create_pj_account_with_pix_and_credentials()
    {
        // 1. Criar usuário PJ
        $user = User::create([
            'first_name' => 'Maria',
            'last_name' => 'Santos Oliveira',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'phone' => '***********',
            'document_type' => 'cnpj',
            'document' => '***********',
            'document_company' => '**************',
            'company_legal_name' => 'Empresa Exemplo Ltda',
            'company_trading_name' => 'Empresa Exemplo',
            'agreement_terms' => true,
            'kyc_status' => 'verified',
        ]);

        // 2. Criar conta PIX para PJ
        $pixAccount = PixAccount::create([
            'user_id' => $user->id,
            'bank_code' => '237',
            'account_number' => '22222-2',
            'account_type' => 'corrente',
            'pix_key' => '**************',
            'pix_key_type' => 'cnpj',
            'balance' => 0,
            'is_active' => true,
        ]);

        // 3. Criar credenciais de API
        $apiKey = ApiKey::create([
            'user_id' => $user->id,
            'key' => 'pj_key_789',
            'secret' => 'pj_secret_101112',
            'description' => 'API Key PJ Empresa',
            'status' => 'active',
        ]);

        // Verificações
        $this->assertTrue($user->isPJ());
        $this->assertFalse($user->isPF());
        $this->assertEquals('**************', $pixAccount->pix_key);
        $this->assertEquals('cnpj', $pixAccount->pix_key_type);
        $this->assertEquals('active', $apiKey->status);

        $this->assertDatabaseHas('users', [
            'document_type' => 'cnpj',
            'document_company' => '**************',
            'company_legal_name' => 'Empresa Exemplo Ltda'
        ]);
    }

    public function test_can_get_account_balance_via_api()
    {
        // Criar usuário e conta PIX com saldo
        $user = User::factory()->create();
        $pixAccount = PixAccount::factory()->create([
            'user_id' => $user->id,
            'balance' => 50000 // R$ 500,00 em centavos
        ]);
        $apiKey = ApiKey::factory()->create(['user_id' => $user->id]);

        $response = $this->withHeaders([
            'api_key' => $apiKey->key,
            'api_secret' => $apiKey->secret,
        ])->getJson("/api/v1/pix-accounts/{$pixAccount->id}/balance");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'balance'
                ]);

        // O sistema converte centavos para reais (50000 centavos = 500 reais)
        $this->assertEquals(500, $response->json('balance'));
    }

    public function test_can_get_webhook_config_via_api()
    {
        // Criar usuário com webhook
        $user = User::factory()->create([
            'webhook_url' => 'https://example.com/webhook'
        ]);
        $apiKey = ApiKey::factory()->create(['user_id' => $user->id]);

        $response = $this->withHeaders([
            'api_key' => $apiKey->key,
            'api_secret' => $apiKey->secret,
        ])->getJson('/api/v1/pix-accounts/webhook');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'webhook_url'
                ]);

        $this->assertEquals('https://example.com/webhook', $response->json('webhook_url'));
    }

    public function test_can_update_webhook_via_api()
    {
        // Criar usuário
        $user = User::factory()->create(['webhook_url' => null]);
        $apiKey = ApiKey::factory()->create(['user_id' => $user->id]);

        $webhookData = [
            'webhook_url' => 'https://newsite.com/webhook'
        ];

        $response = $this->withHeaders([
            'api_key' => $apiKey->key,
            'api_secret' => $apiKey->secret,
        ])->patchJson('/api/v1/pix-accounts/webhook', $webhookData);

        $response->assertStatus(200)
                ->assertJson([
                    'message' => 'Webhook atualizado com sucesso'
                ]);

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'webhook_url' => 'https://newsite.com/webhook'
        ]);
    }

    public function test_api_requires_valid_api_key()
    {
        // Tentar acessar sem API Key
        $response = $this->getJson('/api/v1/pix-accounts/webhook');
        $response->assertStatus(401);

        // Tentar acessar com API Key inválida
        $response = $this->withHeaders([
            'api_key' => 'invalid_key',
            'api_secret' => 'invalid_secret',
        ])->getJson('/api/v1/pix-accounts/webhook');

        $response->assertStatus(401);
    }

    public function test_balance_endpoint_returns_404_for_invalid_account()
    {
        $user = User::factory()->create();
        $apiKey = ApiKey::factory()->create(['user_id' => $user->id]);

        $response = $this->withHeaders([
            'api_key' => $apiKey->key,
            'api_secret' => $apiKey->secret,
        ])->getJson('/api/v1/pix-accounts/999/balance');

        $response->assertStatus(404)
                ->assertJson([
                    'message' => 'Conta PIX não encontrada'
                ]);
    }

    public function test_webhook_update_validates_required_fields()
    {
        $user = User::factory()->create();
        $apiKey = ApiKey::factory()->create(['user_id' => $user->id]);

        $response = $this->withHeaders([
            'api_key' => $apiKey->key,
            'api_secret' => $apiKey->secret,
        ])->patchJson('/api/v1/pix-accounts/webhook', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['webhook_url']);
    }

    public function test_balance_endpoint_only_shows_user_own_accounts()
    {
        // Criar dois usuários
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();

        $pixAccount2 = PixAccount::factory()->create(['user_id' => $user2->id]);
        $apiKey1 = ApiKey::factory()->create(['user_id' => $user1->id]);

        // User 1 tentando acessar conta do User 2
        $response = $this->withHeaders([
            'api_key' => $apiKey1->key,
            'api_secret' => $apiKey1->secret,
        ])->getJson("/api/v1/pix-accounts/{$pixAccount2->id}/balance");

        $response->assertStatus(404); // Deve retornar 404 porque não encontrou a conta para o usuário 1
    }
}
