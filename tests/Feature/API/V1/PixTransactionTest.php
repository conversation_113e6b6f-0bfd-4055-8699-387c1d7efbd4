<?php

namespace Tests\Feature\API\V1;

use Tests\TestCase;
use App\Models\User;
use App\Models\PixAccount;
use App\Models\PixTransaction;
use App\Models\ApiKey;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;

class PixTransactionTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Evita que jobs sejam executados durante os testes
        Queue::fake();
    }

    public function test_can_create_deposit_transaction()
    {
        // Setup
        $user = User::factory()->create();
        $pixAccount = PixAccount::factory()->create(['user_id' => $user->id]);

        // Criar transação de depósito
        $depositData = [
            'user_id' => $user->id,
            'pix_account_id' => $pixAccount->id,
            'pix_transaction_id' => \Illuminate\Support\Str::uuid(),
            'type' => 'deposit',
            'amount' => 10000, // R$ 100,00 em centavos
            'status' => 'pending',
            'external_transaction_id' => 'dep_' . uniqid(),
            'description' => 'Depósito via PIX',
            'pix_key' => '<EMAIL>',
        ];

        $deposit = PixTransaction::create($depositData);

        $this->assertInstanceOf(PixTransaction::class, $deposit);
        $this->assertEquals('deposit', $deposit->type);
        $this->assertEquals(10000, $deposit->amount);
        $this->assertEquals('pending', $deposit->status);

        $this->assertDatabaseHas('pix_transactions', [
            'user_id' => $user->id,
            'type' => 'deposit',
            'amount' => 10000,
            'status' => 'pending'
        ]);
    }

    public function test_can_create_withdraw_transaction()
    {
        // Setup
        $user = User::factory()->create();
        $pixAccount = PixAccount::factory()->create([
            'user_id' => $user->id,
            'balance' => 20000 // R$ 200,00 de saldo
        ]);

        // Criar transação de saque
        $withdrawData = [
            'user_id' => $user->id,
            'pix_account_id' => $pixAccount->id,
            'pix_transaction_id' => \Illuminate\Support\Str::uuid(),
            'type' => 'withdraw',
            'amount' => 5000, // R$ 50,00 em centavos
            'status' => 'pending',
            'external_transaction_id' => 'with_' . uniqid(),
            'description' => 'Saque via PIX',
            'pix_key' => '<EMAIL>',
            'document_beneficiary' => 'João Silva',
        ];

        $withdraw = PixTransaction::create($withdrawData);

        $this->assertInstanceOf(PixTransaction::class, $withdraw);
        $this->assertEquals('withdraw', $withdraw->type);
        $this->assertEquals(5000, $withdraw->amount);
        $this->assertEquals('pending', $withdraw->status);
        $this->assertEquals('<EMAIL>', $withdraw->pix_key);

        $this->assertDatabaseHas('pix_transactions', [
            'user_id' => $user->id,
            'type' => 'withdraw',
            'amount' => 5000,
            'status' => 'pending',
            'pix_key' => '<EMAIL>'
        ]);
    }

    public function test_deposit_transaction_has_predefined_type()
    {
        // Setup
        $user = User::factory()->create();
        $pixAccount = PixAccount::factory()->create(['user_id' => $user->id]);

        // Criar transação de depósito
        $deposit = PixTransaction::create([
            'user_id' => $user->id,
            'pix_account_id' => $pixAccount->id,
            'pix_transaction_id' => \Illuminate\Support\Str::uuid(),
            'type' => 'deposit', // Tipo predefinido
            'amount' => 15000,
            'status' => 'pending',
            'external_transaction_id' => 'dep_predefined_001',
            'description' => 'Depósito com tipo predefinido',
            'pix_key' => '<EMAIL>',
        ]);

        // Verificar que o tipo foi definido corretamente
        $this->assertEquals('deposit', $deposit->type);
        $this->assertNotEquals('withdraw', $deposit->type);
        $this->assertNotEquals('transfer', $deposit->type);

        // Verificar que não é possível alterar o tipo após criação
        $originalType = $deposit->type;
        $deposit->type = 'withdraw';
        $this->assertEquals('withdraw', $deposit->type); // Mudou localmente

        // Mas no banco deve manter o original até ser salvo
        $deposit->refresh();
        $this->assertEquals($originalType, $deposit->type);
    }

    public function test_withdraw_transaction_has_predefined_type()
    {
        // Setup
        $user = User::factory()->create();
        $pixAccount = PixAccount::factory()->create([
            'user_id' => $user->id,
            'balance' => 30000
        ]);

        // Criar transação de saque
        $withdraw = PixTransaction::create([
            'user_id' => $user->id,
            'pix_account_id' => $pixAccount->id,
            'pix_transaction_id' => \Illuminate\Support\Str::uuid(),
            'type' => 'withdraw', // Tipo predefinido
            'amount' => 8000,
            'status' => 'pending',
            'external_transaction_id' => 'with_predefined_001',
            'description' => 'Saque com tipo predefinido',
            'pix_key' => '<EMAIL>',
            'document_beneficiary' => 'Beneficiário Teste',
        ]);

        // Verificar que o tipo foi definido corretamente
        $this->assertEquals('withdraw', $withdraw->type);
        $this->assertNotEquals('deposit', $withdraw->type);
        $this->assertNotEquals('transfer', $withdraw->type);

        // Verificar que possui beneficiário para saques
        $this->assertNotNull($withdraw->document_beneficiary);
        $this->assertEquals('Beneficiário Teste', $withdraw->document_beneficiary);
    }

    public function test_transaction_types_are_restricted()
    {
        // Setup
        $user = User::factory()->create();
        $pixAccount = PixAccount::factory()->create(['user_id' => $user->id]);

        // Testar apenas tipos válidos
        $validTypes = ['deposit', 'withdraw'];

        foreach ($validTypes as $type) {
            $transaction = PixTransaction::create([
                'user_id' => $user->id,
                'pix_account_id' => $pixAccount->id,
                'pix_transaction_id' => \Illuminate\Support\Str::uuid(),
                'type' => $type,
                'amount' => 5000,
                'status' => 'pending',
                'external_transaction_id' => $type . '_' . uniqid(),
                'description' => 'Transação do tipo ' . $type,
                'pix_key' => '<EMAIL>',
            ]);

            $this->assertEquals($type, $transaction->type);
            $this->assertDatabaseHas('pix_transactions', [
                'type' => $type,
                'external_transaction_id' => $transaction->external_transaction_id
            ]);
        }
    }

    public function test_transactions_belong_to_pix_account()
    {
        // Setup
        $user = User::factory()->create();
        $pixAccount = PixAccount::factory()->create(['user_id' => $user->id]);

        // Criar múltiplas transações para a mesma conta
        $deposit = PixTransaction::factory()->create([
            'user_id' => $user->id,
            'pix_account_id' => $pixAccount->id,
            'type' => 'deposit'
        ]);

        $withdraw = PixTransaction::factory()->create([
            'user_id' => $user->id,
            'pix_account_id' => $pixAccount->id,
            'type' => 'withdraw'
        ]);

        // Verificar relacionamentos
        $this->assertEquals($pixAccount->id, $deposit->pix_account_id);
        $this->assertEquals($pixAccount->id, $withdraw->pix_account_id);
        $this->assertEquals($user->id, $deposit->user_id);
        $this->assertEquals($user->id, $withdraw->user_id);

        // Verificar que a conta PIX possui as transações
        $pixAccountTransactions = PixTransaction::where('pix_account_id', $pixAccount->id)->get();
        $this->assertCount(2, $pixAccountTransactions);
        $this->assertTrue($pixAccountTransactions->contains($deposit));
        $this->assertTrue($pixAccountTransactions->contains($withdraw));
    }

    public function test_can_create_deposit_via_api()
    {
        // Setup
        $user = User::factory()->create();
        $pixAccount = PixAccount::factory()->create(['user_id' => $user->id]);
        $apiKey = ApiKey::factory()->create(['user_id' => $user->id]);

        $depositData = [
            'amount' => 10000, // R$ 100,00
            'description' => 'Depósito via API',
            'external_id' => 'ext_' . uniqid(),
            'document_beneficiary' => $user->document, // Campo obrigatório
            'name' => $user->first_name . ' ' . $user->last_name, // Campo obrigatório
        ];

        $response = $this->withHeaders([
            'api_key' => $apiKey->key,
            'api_secret' => $apiKey->secret,
        ])->postJson('/api/v1/pix-transactions/qrcode', $depositData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'payment_company',
                    'transaction_id_external',
                    'beneficiary',
                    'document_beneficiary',
                    'pix_key',
                    'amount',
                    'status',
                    'type',
                    'qr_code',
                    'qr_code_copy_paste',
                    'created_at'
                ]);

        $this->assertEquals('deposit', $response->json('type'));
        $this->assertEquals(100, $response->json('amount')); // Convertido para reais
        $this->assertEquals('pending', $response->json('status'));

        $this->assertDatabaseHas('pix_transactions', [
            'user_id' => $user->id,
            'type' => 'deposit',
            'amount' => 10000, // Valor original em centavos
            'description' => 'Depósito via API'
        ]);
    }

    public function test_can_create_withdraw_via_api()
    {
        // Setup - usuário com saldo
        $user = User::factory()->create();
        $pixAccount = PixAccount::factory()->create([
            'user_id' => $user->id,
            'balance' => 50000 // R$ 500,00
        ]);
        $apiKey = ApiKey::factory()->create(['user_id' => $user->id]);

        $withdrawData = [
            'amount' => 10000, // R$ 100,00
            'description' => 'Saque via API',
            'pix_key' => '<EMAIL>',
            'document_beneficiary' => 'João Silva',
            'external_id' => 'ext_' . uniqid(),
        ];

        $response = $this->withHeaders([
            'api_key' => $apiKey->key,
            'api_secret' => $apiKey->secret,
        ])->postJson('/api/v1/pix-transactions/withdraw', $withdrawData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'transaction' => [
                        'id',
                        'type',
                        'amount',
                        'status',
                        'description',
                        'pix_key',
                        'document_beneficiary'
                    ]
                ]);

        $this->assertEquals('withdraw', $response->json('transaction.type'));
        $this->assertEquals(10000, $response->json('transaction.amount'));
        $this->assertEquals('<EMAIL>', $response->json('transaction.pix_key'));

        $this->assertDatabaseHas('pix_transactions', [
            'user_id' => $user->id,
            'type' => 'withdraw',
            'amount' => 10000,
            'pix_key' => '<EMAIL>',
            'document_beneficiary' => 'João Silva'
        ]);
    }

    public function test_can_get_transactions_via_api()
    {
        // Setup
        $user = User::factory()->create();
        $pixAccount = PixAccount::factory()->create(['user_id' => $user->id]);
        $apiKey = ApiKey::factory()->create(['user_id' => $user->id]);

        // Criar transações
        $deposit = PixTransaction::factory()->create([
            'user_id' => $user->id,
            'pix_account_id' => $pixAccount->id,
            'type' => 'deposit'
        ]);

        $withdraw = PixTransaction::factory()->create([
            'user_id' => $user->id,
            'pix_account_id' => $pixAccount->id,
            'type' => 'withdraw'
        ]);

        $response = $this->withHeaders([
            'api_key' => $apiKey->key,
            'api_secret' => $apiKey->secret,
        ])->getJson('/api/v1/pix-transactions');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        '*' => [
                            'id',
                            'type',
                            'amount',
                            'status',
                            'description',
                            'created_at'
                        ]
                    ]
                ]);

        $transactionIds = collect($response->json('data'))->pluck('id')->toArray();
        $this->assertContains($deposit->id, $transactionIds);
        $this->assertContains($withdraw->id, $transactionIds);
    }

    public function test_api_validates_withdraw_amount_against_balance()
    {
        // Setup - usuário com saldo insuficiente
        $user = User::factory()->create();
        $pixAccount = PixAccount::factory()->create([
            'user_id' => $user->id,
            'balance' => 5000 // R$ 50,00
        ]);
        $apiKey = ApiKey::factory()->create(['user_id' => $user->id]);

        $withdrawData = [
            'amount' => 10000, // R$ 100,00 - maior que o saldo
            'description' => 'Saque com saldo insuficiente',
            'pix_key' => '<EMAIL>',
            'document_beneficiary' => 'João Silva',
        ];

        $response = $this->withHeaders([
            'api_key' => $apiKey->key,
            'api_secret' => $apiKey->secret,
        ])->postJson('/api/v1/pix-transactions/withdraw', $withdrawData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['amount']);
    }

    public function test_api_validates_required_fields_for_deposit()
    {
        $user = User::factory()->create();
        $apiKey = ApiKey::factory()->create(['user_id' => $user->id]);

        $response = $this->withHeaders([
            'api_key' => $apiKey->key,
            'api_secret' => $apiKey->secret,
        ])->postJson('/api/v1/pix-transactions/qrcode', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['amount']);
    }

    public function test_api_validates_required_fields_for_withdraw()
    {
        $user = User::factory()->create();
        $apiKey = ApiKey::factory()->create(['user_id' => $user->id]);

        $response = $this->withHeaders([
            'api_key' => $apiKey->key,
            'api_secret' => $apiKey->secret,
        ])->postJson('/api/v1/pix-transactions/withdraw', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors([
                    'amount',
                    'pix_key',
                    'document_beneficiary'
                ]);
    }

    public function test_user_can_only_see_own_transactions()
    {
        // Criar dois usuários
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();

        $pixAccount1 = PixAccount::factory()->create(['user_id' => $user1->id]);
        $pixAccount2 = PixAccount::factory()->create(['user_id' => $user2->id]);

        $transaction1 = PixTransaction::factory()->create([
            'user_id' => $user1->id,
            'pix_account_id' => $pixAccount1->id
        ]);

        $transaction2 = PixTransaction::factory()->create([
            'user_id' => $user2->id,
            'pix_account_id' => $pixAccount2->id
        ]);

        $apiKey1 = ApiKey::factory()->create(['user_id' => $user1->id]);

        $response = $this->withHeaders([
            'api_key' => $apiKey1->key,
            'api_secret' => $apiKey1->secret,
        ])->getJson('/api/v1/pix-transactions');

        $response->assertStatus(200);

        $transactionIds = collect($response->json('data'))->pluck('id')->toArray();
        $this->assertContains($transaction1->id, $transactionIds);
        $this->assertNotContains($transaction2->id, $transactionIds);
    }
}
