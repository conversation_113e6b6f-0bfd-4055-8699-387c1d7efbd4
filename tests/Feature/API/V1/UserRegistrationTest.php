<?php

namespace Tests\Feature\API\V1;

use Tests\TestCase;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class UserRegistrationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('public');
    }

    public function test_can_create_pf_account()
    {
        $userData = [
            'first_name' => '<PERSON>',
            'last_name' => '<PERSON><PERSON>',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'phone' => '***********',
            'document_type' => 'cpf',
            'document' => '***********',
            'agreement_terms' => true,
            'kyc_status' => 'pending',
        ];

        $user = User::create($userData);

        $this->assertInstanceOf(User::class, $user);
        $this->assertEquals('Lucas', $user->first_name);
        $this->assertEquals('cpf', $user->document_type);
        $this->assertEquals('***********', $user->document);
        $this->assertEquals('pending', $user->kyc_status);
        $this->assertTrue($user->isPF());
        $this->assertFalse($user->isPJ());

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'document_type' => 'cpf',
            'document' => '***********',
            'kyc_status' => 'pending'
        ]);
    }

    public function test_can_create_pj_account()
    {
        $userData = [
            'first_name' => 'Lucas',
            'last_name' => 'Mateus Alves da Silva',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'phone' => '***********',
            'document_type' => 'cnpj',
            'document' => '***********',
            'document_company' => '**************',
            'company_legal_name' => 'doce maré atacado ltda',
            'company_trading_name' => 'Doce Maré',
            'agreement_terms' => true,
            'kyc_status' => 'pending',
        ];

        $user = User::create($userData);

        $this->assertInstanceOf(User::class, $user);
        $this->assertEquals('Lucas', $user->first_name);
        $this->assertEquals('cnpj', $user->document_type);
        $this->assertEquals('***********', $user->document);
        $this->assertEquals('**************', $user->document_company);
        $this->assertEquals('doce maré atacado ltda', $user->company_legal_name);
        $this->assertTrue($user->isPJ());
        $this->assertFalse($user->isPF());

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'document_type' => 'cnpj',
            'document' => '***********',
            'document_company' => '**************',
            'company_legal_name' => 'doce maré atacado ltda'
        ]);
    }

    public function test_can_register_pf_via_api()
    {
        $registrationData = [
            'first_name' => 'José',
            'last_name' => 'Silva Santos',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone' => '11988776655',
            'document_type' => 'cpf',
            'document' => '11122233344',
            'agreement_terms' => true,
            'document_front' => UploadedFile::fake()->image('document_front.jpg'),
            'document_back' => UploadedFile::fake()->image('document_back.jpg'),
            'selfie' => UploadedFile::fake()->image('selfie.jpg'),
        ];

        $response = $this->postJson('/api/v1/auth/register', $registrationData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'user' => [
                        'id',
                        'first_name',
                        'last_name',
                        'email',
                        'document_type',
                        'document',
                        'kyc_status'
                    ],
                    'credentials' => [
                        'api_key',
                        'api_secret'
                    ]
                ]);

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'document_type' => 'cpf',
            'document' => '11122233344',
            'kyc_status' => 'pending'
        ]);

        $user = User::where('email', '<EMAIL>')->first();
        $this->assertTrue($user->isPF());
        $this->assertFalse($user->isPJ());
    }

    public function test_can_register_pj_via_api()
    {
        $registrationData = [
            'first_name' => 'Maria',
            'last_name' => 'Oliveira Santos',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone' => '11977665544',
            'document_type' => 'cnpj',
            'document' => '55566677788',
            'document_company' => '12345678000123',
            'company_legal_name' => 'Minha Empresa Ltda',
            'company_trading_name' => 'Minha Empresa',
            'agreement_terms' => true,
            'company_contract' => UploadedFile::fake()->create('company_contract.pdf'),
            'company_statute' => UploadedFile::fake()->create('company_statute.pdf'),
            'company_proxy' => UploadedFile::fake()->create('company_proxy.pdf'),
        ];

        $response = $this->postJson('/api/v1/auth/register', $registrationData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'user' => [
                        'id',
                        'first_name',
                        'last_name',
                        'email',
                        'document_type',
                        'document',
                        'document_company',
                        'company_legal_name',
                        'company_trading_name',
                        'kyc_status'
                    ],
                    'credentials' => [
                        'api_key',
                        'api_secret'
                    ]
                ]);

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'document_type' => 'cnpj',
            'document' => '55566677788',
            'document_company' => '12345678000123',
            'company_legal_name' => 'Minha Empresa Ltda',
            'kyc_status' => 'pending'
        ]);

        $user = User::where('email', '<EMAIL>')->first();
        $this->assertTrue($user->isPJ());
        $this->assertFalse($user->isPF());
    }

    public function test_api_registration_validates_required_fields()
    {
        // Testar validação de campos obrigatórios
        $response = $this->postJson('/api/v1/auth/register', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors([
                    'first_name',
                    'last_name',
                    'email',
                    'phone',
                    'document',
                    'document_type'
                ]);
    }

    public function test_api_registration_validates_cpf_requirements()
    {
        $registrationData = [
            'first_name' => 'Teste',
            'last_name' => 'Usuario',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone' => '11999888777',
            'document_type' => 'cpf',
            'document' => '99988877766',
            'agreement_terms' => true,
            // Não incluir documentos obrigatórios para CPF
        ];

        $response = $this->postJson('/api/v1/auth/register', $registrationData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors([
                    'document_front',
                    'document_back',
                    'selfie'
                ]);
    }

    public function test_api_registration_validates_cnpj_requirements()
    {
        $registrationData = [
            'first_name' => 'Empresa',
            'last_name' => 'Teste',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone' => '11999888777',
            'document_type' => 'cnpj',
            'document' => '99988877766',
            'agreement_terms' => true,
            // Não incluir campos obrigatórios para CNPJ
        ];

        $response = $this->postJson('/api/v1/auth/register', $registrationData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors([
                    'document_company',
                    'company_legal_name',
                    'company_trading_name',
                    'company_contract',
                    'company_statute',
                    'company_proxy'
                ]);
    }

    public function test_api_registration_validates_unique_email()
    {
        // Criar usuário existente
        User::factory()->create(['email' => '<EMAIL>']);

        $registrationData = [
            'first_name' => 'Novo',
            'last_name' => 'Usuario',
            'email' => '<EMAIL>', // E-mail já existe
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone' => '11999888777',
            'document_type' => 'cpf',
            'document' => '99988877766',
            'agreement_terms' => true,
            'document_front' => UploadedFile::fake()->image('document_front.jpg'),
            'document_back' => UploadedFile::fake()->image('document_back.jpg'),
            'selfie' => UploadedFile::fake()->image('selfie.jpg'),
        ];

        $response = $this->postJson('/api/v1/auth/register', $registrationData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email']);
    }

    public function test_api_registration_validates_unique_document()
    {
        // Criar usuário existente
        User::factory()->create([
            'document_type' => 'cpf',
            'document' => '12345678901'
        ]);

        $registrationData = [
            'first_name' => 'Novo',
            'last_name' => 'Usuario',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone' => '11999888777',
            'document_type' => 'cpf',
            'document' => '12345678901', // CPF já existe
            'agreement_terms' => true,
            'document_front' => UploadedFile::fake()->image('document_front.jpg'),
            'document_back' => UploadedFile::fake()->image('document_back.jpg'),
            'selfie' => UploadedFile::fake()->image('selfie.jpg'),
        ];

        $response = $this->postJson('/api/v1/auth/register', $registrationData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['document']);
    }

    public function test_can_login_via_api()
    {
        // Criar usuário para login
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password123')
        ]);

        $loginData = [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ];

        $response = $this->postJson('/api/v1/auth/login', $loginData);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'user' => [
                        'id',
                        'first_name',
                        'last_name',
                        'email'
                    ],
                    'access_token',
                    'token_type'
                ]);

        $this->assertEquals('<EMAIL>', $response->json('user.email'));
        $this->assertEquals('Bearer', $response->json('token_type'));
        $this->assertNotNull($response->json('access_token'));
    }

    public function test_api_login_validates_credentials()
    {
        // Criar usuário
        User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('senha_correta')
        ]);

        // Tentar login com senha errada
        $response = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'senha_errada'
        ]);

        $response->assertStatus(401);
    }

    public function test_api_login_validates_required_fields()
    {
        $response = $this->postJson('/api/v1/auth/login', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email', 'password']);
    }

    public function test_can_get_user_profile_via_api()
    {
        // Criar usuário e fazer login
        $user = User::factory()->create();
        $token = $user->createToken('test')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/v1/me');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'user' => [
                        'id',
                        'first_name',
                        'last_name',
                        'email',
                        'document_type'
                    ]
                ]);

        $this->assertEquals($user->id, $response->json('user.id'));
        $this->assertEquals($user->email, $response->json('user.email'));
    }

    public function test_can_logout_via_api()
    {
        // Criar usuário e fazer login
        $user = User::factory()->create();
        $token = $user->createToken('test')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/v1/logout');

        $response->assertStatus(200)
                ->assertJson([
                    'message' => 'Você saiu com sucesso!'
                ]);
    }

    public function test_api_requires_authentication_for_protected_routes()
    {
        // Tentar acessar rota protegida sem token
        $response = $this->getJson('/api/v1/me');
        $response->assertStatus(401);

        // Tentar fazer logout sem token
        $response = $this->postJson('/api/v1/logout');
        $response->assertStatus(401);
    }
}
