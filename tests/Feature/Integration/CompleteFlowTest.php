<?php

namespace Tests\Feature\Integration;

use Tests\TestCase;
use App\Models\User;
use App\Models\PixAccount;
use App\Models\PixTransaction;
use App\Models\ApiKey;
use Illuminate\Foundation\Testing\RefreshDatabase;

class CompleteFlowTest extends TestCase
{
    use RefreshDatabase;

    public function test_complete_flow_pf_user_with_transactions()
    {
        // 1. Criar usuário PF
        $user = User::create([
            'first_name' => '<PERSON>',
            'last_name' => 'Mat<PERSON>',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'phone' => '***********',
            'document_type' => 'cpf',
            'document' => '***********',
            'agreement_terms' => true,
            'kyc_status' => 'verified'
        ]);

        // 2. Criar conta PIX
        $pixAccount = PixAccount::create([
            'user_id' => $user->id,
            'bank_code' => '001',
            'account_number' => '12345-6',
            'account_type' => 'corrente',
            'pix_key' => '<EMAIL>',
            'pix_key_type' => 'email',
            'balance' => 0,
            'is_active' => true,
        ]);

        // 3. Criar API Key
        $apiKey = ApiKey::create([
            'user_id' => $user->id,
            'key' => 'lucas_key_123',
            'secret' => 'lucas_secret_456',
            'description' => 'API Key do Lucas',
            'status' => 'active',
        ]);

        // 4. Criar transação de depósito
        $deposit = PixTransaction::create([
            'user_id' => $user->id,
            'pix_account_id' => $pixAccount->id,
            'pix_transaction_id' => \Illuminate\Support\Str::uuid(),
            'type' => 'deposit',
            'amount' => 15000,
            'status' => 'completed',
            'external_transaction_id' => 'dep_lucas_001',
            'description' => 'Depósito Lucas',
            'pix_key' => '<EMAIL>',
        ]);

        // 5. Criar transação de saque
        $withdraw = PixTransaction::create([
            'user_id' => $user->id,
            'pix_account_id' => $pixAccount->id,
            'pix_transaction_id' => \Illuminate\Support\Str::uuid(),
            'type' => 'withdraw',
            'amount' => 5000,
            'status' => 'pending',
            'external_transaction_id' => 'with_lucas_001',
            'description' => 'Saque Lucas',
            'pix_key' => '<EMAIL>',
            'document_beneficiary' => 'João Silva',
        ]);

        // 6. Atualizar saldo da conta
        $pixAccount->update(['balance' => 10000]); // R$ 150 - R$ 50 = R$ 100

        // 7. Configurar webhook no usuário
        $user->update(['webhook_url' => 'https://lucas.com/webhook']);

        // Verificações finais
        $this->assertEquals(1, User::where('document', '***********')->count());
        $this->assertEquals(1, PixAccount::where('user_id', $user->id)->count());
        $this->assertEquals(1, ApiKey::where('user_id', $user->id)->count());
        $this->assertEquals('https://lucas.com/webhook', $user->fresh()->webhook_url);
    }

    public function test_complete_flow_pj_user_with_transactions()
    {
        // 1. Criar usuário PJ
        $user = User::create([
            'first_name' => 'Lucas',
            'last_name' => 'Mateus Alves da Silva',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'phone' => '***********',
            'document_type' => 'cnpj',
            'document' => '***********',
            'document_company' => '**************',
            'company_legal_name' => 'doce maré atacado ltda',
            'company_trading_name' => 'Doce Maré',
            'agreement_terms' => true,
            'kyc_status' => 'verified'
        ]);

        // 2. Criar conta PIX empresarial
        $pixAccount = PixAccount::create([
            'user_id' => $user->id,
            'bank_code' => '237',
            'account_number' => '98765-4',
            'account_type' => 'corrente',
            'pix_key' => '**************',
            'pix_key_type' => 'cnpj',
            'balance' => 0,
            'is_active' => true,
        ]);

        // 3. Criar API Key empresarial
        $apiKey = ApiKey::create([
            'user_id' => $user->id,
            'key' => 'empresa_key_789',
            'secret' => 'empresa_secret_101112',
            'description' => 'API Key Doce Maré',
            'status' => 'active',
        ]);

        // 4. Criar múltiplos depósitos
        $deposit1 = PixTransaction::create([
            'user_id' => $user->id,
            'pix_account_id' => $pixAccount->id,
            'pix_transaction_id' => \Illuminate\Support\Str::uuid(),
            'type' => 'deposit',
            'amount' => 50000,
            'status' => 'completed',
            'external_transaction_id' => 'dep_empresa_001',
            'description' => 'Vendas semana 1',
            'pix_key' => '**************',
        ]);

        $deposit2 = PixTransaction::create([
            'user_id' => $user->id,
            'pix_account_id' => $pixAccount->id,
            'pix_transaction_id' => \Illuminate\Support\Str::uuid(),
            'type' => 'deposit',
            'amount' => 75000,
            'status' => 'completed',
            'external_transaction_id' => 'dep_empresa_002',
            'description' => 'Vendas semana 2',
            'pix_key' => '**************',
        ]);

        // 5. Criar saque empresarial
        $withdraw = PixTransaction::create([
            'user_id' => $user->id,
            'pix_account_id' => $pixAccount->id,
            'pix_transaction_id' => \Illuminate\Support\Str::uuid(),
            'type' => 'withdraw',
            'amount' => 25000,
            'status' => 'completed',
            'external_transaction_id' => 'with_empresa_001',
            'description' => 'Pagamento fornecedor',
            'pix_key' => '<EMAIL>',
            'document_beneficiary' => 'Fornecedor XYZ Ltda',
        ]);

        // 6. Atualizar saldo final
        $pixAccount->update(['balance' => 100000]); // R$ 500 + R$ 750 - R$ 250 = R$ 1000

        // 7. Configurar webhook empresarial
        $user->update(['webhook_url' => 'https://empresa.com/api/webhook']);

        // Verificações finais
        $this->assertEquals(1, User::where('document_company', '**************')->count());
        $this->assertEquals(100000, $pixAccount->fresh()->balance);
        $this->assertEquals(2, PixTransaction::where('user_id', $user->id)->where('type', 'deposit')->count());
        $this->assertEquals(1, PixTransaction::where('user_id', $user->id)->where('type', 'withdraw')->count());
        $this->assertEquals('https://empresa.com/api/webhook', $user->fresh()->webhook_url);
    }
}
