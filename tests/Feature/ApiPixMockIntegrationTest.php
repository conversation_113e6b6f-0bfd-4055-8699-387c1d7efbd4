<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\PixAccount;
use App\Models\ApiKey;
use App\Adapters\ApiPixAdapter;
use App\Services\PixTransactionService;
use App\Services\WebhookService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Config;

class ApiPixMockIntegrationTest extends TestCase
{
  use RefreshDatabase;

  protected function setUp(): void
  {
    parent::setUp();

    // Força modo mock para os testes
    Config::set('app.env', 'testing');

    // Evita que jobs sejam executados durante os testes
    Queue::fake();
  }

  public function test_complete_pix_deposit_flow_with_mock_adapter()
  {
    // 1. Setup - Criar usuário e conta PIX
    $user = User::factory()->create([
      'document_type' => 'cpf',
      'document' => '***********',
      'first_name' => 'João',
      'last_name' => 'Silva'
    ]);

    $pixAccount = PixAccount::factory()->create([
      'user_id' => $user->id,
      'pix_key' => '<EMAIL>',
      'pix_key_type' => 'email',
      'is_active' => true
    ]);

    $apiKey = ApiKey::factory()->create([
      'user_id' => $user->id,
      'status' => 'active'
    ]);

    // 2. Configurar adapter em modo mock
    $adapter = new ApiPixAdapter();
    $adapter->setMockMode(true);

    // 3. Testar geração de token
    $token = $adapter->getToken();
    $this->assertNotEmpty($token);
    $this->assertTrue($adapter->isTokenValid());
    $this->assertTrue($adapter->isMockMode());

    // 4. Preparar dados do depósito
    $depositData = [
      "calendario" => [
        "expiracao" => 7200
      ],
      "devedor" => [
        "cnpj" => "**************",
        "nome" => "João Silva"
      ],
      "valor" => [
        "original" => "150.75",
        "modalidadeAlteracao" => 1
      ],
      "chave" => $pixAccount->pix_key,
      "solicitacaoPagador" => "Depósito PIX - Teste Mock"
    ];

    // 5. Criar depósito PIX usando adapter mock
    $response = $adapter->createPixDeposit($depositData);

    // 6. Verificar resposta do depósito
    $this->assertIsArray($response);
    $this->assertEquals('ATIVA', $response['status']);
    $this->assertArrayHasKey('txid', $response);
    $this->assertArrayHasKey('pixCopiaECola', $response);
    $this->assertArrayHasKey('location', $response);

    // Verificar se os dados foram preservados
    $this->assertEquals($depositData['devedor']['nome'], $response['devedor']['nome']);
    $this->assertEquals($depositData['valor']['original'], $response['valor']['original']);
    $this->assertEquals($depositData['chave'], $response['chave']);
    $this->assertEquals($depositData['solicitacaoPagador'], $response['solicitacaoPagador']);

    // 7. Testar consulta da transação
    $txid = $response['txid'];
    $transactionResponse = $adapter->getPixTransaction($txid);

    $this->assertIsArray($transactionResponse);
    $this->assertEquals($txid, $transactionResponse['txid']);
    $this->assertArrayHasKey('status', $transactionResponse);
    $this->assertContains($transactionResponse['status'], ['ATIVA', 'CONCLUIDA', 'REMOVIDA_PELO_USUARIO_RECEBEDOR']);

    // 8. Verificar estrutura completa da resposta
    $this->assertArrayHasKey('calendario', $transactionResponse);
    $this->assertArrayHasKey('devedor', $transactionResponse);
    $this->assertArrayHasKey('valor', $transactionResponse);
    $this->assertArrayHasKey('location', $transactionResponse);
  }

  public function test_pix_transaction_service_with_mock_adapter()
  {
    // Setup
    $user = User::factory()->create();
    $pixAccount = PixAccount::factory()->create(['user_id' => $user->id]);

    // Simular webhook service
    $webhookService = $this->createMock(WebhookService::class);
    $pixTransactionService = new PixTransactionService($webhookService);

    // Dados do depósito
    $depositData = [
      'amount' => 100.50,
      'document_beneficiary' => '**************',
      'name' => 'Empresa Teste LTDA',
      'description' => 'Depósito via PIX Mock'
    ];

    // Criar depósito através do service
    $result = $pixTransactionService->createDeposit($user, $pixAccount, $depositData);

    // Verificar se o depósito foi criado
    $this->assertNotNull($result);
    $this->assertInstanceOf(\App\Http\Resources\PixTransactionDepositResource::class, $result);

    // Verificar se foi criado no banco
    $this->assertDatabaseHas('pix_transactions', [
      'user_id' => $user->id,
      'pix_account_id' => $pixAccount->id,
      'type' => 'deposit',
      'amount' => 10050, // Valor em centavos
      'status' => 'pending'
    ]);
  }

  public function test_api_endpoint_with_mock_mode()
  {
    // Setup
    $user = User::factory()->create();
    $pixAccount = PixAccount::factory()->create(['user_id' => $user->id]);
    $apiKey = ApiKey::factory()->create(['user_id' => $user->id]);

    // Dados da requisição
    $requestData = [
      'amount' => 75.25,
      'document_beneficiary' => '**************',
      'name' => 'Cliente Teste'
    ];

    // Fazer requisição para o endpoint
    $response = $this->withHeaders([
      'api_key' => $apiKey->key,
      'api_secret' => $apiKey->secret,
    ])->postJson('/api/v1/pix-transactions/qrcode', $requestData);

    // Verificar resposta
    $response->assertStatus(201)
      ->assertJsonStructure([
        'payment_company',
        'transaction_id_external',
        'beneficiary',
        'document_beneficiary',
        'pix_key',
        'amount',
        'status',
        'type',
        'created_at'
      ]);

    $this->assertEquals('deposit', $response->json('type'));
    $this->assertEquals(75.25, $response->json('amount'));
    $this->assertEquals('pending', $response->json('status'));
  }

  public function test_mock_adapter_generates_unique_data()
  {
    $adapter = new ApiPixAdapter();
    $adapter->setMockMode(true);

    $baseData = [
      "valor" => ["original" => "50.00"],
      "devedor" => ["cnpj" => "**************", "nome" => "Teste"],
      "chave" => "<EMAIL>"
    ];

    // Criar múltiplos depósitos
    $response1 = $adapter->createPixDeposit($baseData);
    $response2 = $adapter->createPixDeposit($baseData);
    $response3 = $adapter->createPixDeposit($baseData);

    // Verificar que os dados únicos são diferentes
    $this->assertNotEquals($response1['txid'], $response2['txid']);
    $this->assertNotEquals($response2['txid'], $response3['txid']);
    $this->assertNotEquals($response1['loc']['id'], $response2['loc']['id']);
    $this->assertNotEquals($response1['location'], $response2['location']);
    $this->assertNotEquals($response1['pixCopiaECola'], $response2['pixCopiaECola']);

    // Mas os dados fixos devem ser iguais
    $this->assertEquals($response1['status'], $response2['status']);
    $this->assertEquals($response1['devedor'], $response2['devedor']);
    $this->assertEquals($response1['valor'], $response2['valor']);
  }

  public function test_mock_transaction_status_variety()
  {
    $adapter = new ApiPixAdapter();
    $adapter->setMockMode(true);

    $statuses = [];

    // Testar com diferentes IDs para obter diferentes status
    for ($i = 0; $i < 20; $i++) {
      $transactionId = 'test-transaction-' . $i;
      $response = $adapter->getPixTransaction($transactionId);
      $statuses[] = $response['status'];
    }

    // Verificar que temos variedade de status
    $uniqueStatuses = array_unique($statuses);
    $this->assertGreaterThan(1, count($uniqueStatuses));

    // Verificar que todos os status são válidos
    $validStatuses = ['ATIVA', 'CONCLUIDA', 'REMOVIDA_PELO_USUARIO_RECEBEDOR'];
    foreach ($uniqueStatuses as $status) {
      $this->assertContains($status, $validStatuses);
    }
  }

  public function test_mock_mode_environment_detection()
  {
    // Testar com variável de ambiente
    putenv('API_PIX_MOCK_MODE=true');
    $adapter1 = new ApiPixAdapter();
    $this->assertTrue($adapter1->isMockMode());

    putenv('API_PIX_MOCK_MODE=false');
    $adapter2 = new ApiPixAdapter();
    $this->assertFalse($adapter2->isMockMode());

    // Limpar variável de ambiente
    putenv('API_PIX_MOCK_MODE');
  }
}
