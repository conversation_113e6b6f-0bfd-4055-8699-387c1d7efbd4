<template>
  <div class="bg-[#111827] overflow-x-hidden">
    <RouterView />
    <GlobalToast
      v-if="toast.state.isVisible"
      :type="toast.state.type"
      :message="toast.state.message"
      @close="toast.hideToast"
      class="fixed top-4 right-4 z-50"
    />
  </div>
</template>

<script setup lang="ts">
import GlobalToast from '@/components/ui/toast/GlobalToast.vue'
import { useGlobalToastStore } from '@/stores/global-toast'

const toast = useGlobalToastStore()
</script>
