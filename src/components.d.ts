/* eslint-disable */
// This file is automatically generated to help TypeScript find the type definitions for imported Vue components
// Generated by vue-tsc
// We recommend not editing this file directly

import type { DefineComponent } from 'vue'

// Landing page components
declare module '@/components/landing-page/Navbar.vue' {
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module '@/components/landing-page/ServicesSection.vue' {
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module '@/components/landing-page/BackToTopButton.vue' {
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module '@/components/landing-page/FooterSection.vue' {
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module '@/components/landing-page/HeroSection.vue' {
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module '@/components/landing-page/AboutSection.vue' {
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module '@/components/landing-page/CtaSection.vue' {
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module '@/components/landing-page/ServiceCard.vue' {
  const component: DefineComponent<{}, {}, any>
  export default component
}

// Layouts
declare module '@/layouts/HomeLayout.vue' {
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module '@/layouts/AuthLayout.vue' {
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module '@/layouts/DashboardLayout.vue' {
  const component: DefineComponent<{}, {}, any>
  export default component
}

// Views
declare module '@/views/landing-page/HomeView.vue' {
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module '@/views/auth/LoginView.vue' {
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module '@/views/auth/RegisterView.vue' {
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module '@/views/auth/ResetPasswordView.vue' {
  const component: DefineComponent<{}, {}, any>
  export default component
}
