import axios from "axios";
import { has } from "lodash";
import { useRoute, useRouter } from "vue-router";

export function useApiService() {
  const route = useRoute();
  const router = useRouter();

  const api = axios.create({
    baseURL:
      import.meta.env.MODE !== 'production'
        ? 'http://localhost:8000/api/v1'
        : 'https://api.global.com.br/api/v1',
    withCredentials: true,
  })

  const initCsrf = async () => {
    try {
      await axios.get(
        import.meta.env.MODE !== 'production'
          ? 'http://localhost:8000/sanctum/csrf-cookie'
          : 'https://api.global.com.br/sanctum/csrf-cookie',
        { withCredentials: true },
      )
      return true;
    } catch (error) {
      console.error("Erro ao inicializar CSRF:", error);
      return false;
    }
  };

  api.interceptors.request.use(
    function (config) {
      if (localStorage.getItem("token")) {
        const token = localStorage.getItem("token");
        config.headers.Authorization = `Bearer ${token}`;
      }
      initCsrf();
      return config;
    },
    function (error) {
      return Promise.reject(error);
    }
  );

  api.interceptors.response.use(
    function (response) {
      if (has(response, "data.token")) {
        localStorage.setItem("token", response.data.token);
      }
      initCsrf()
      return response;
    },
    function (error) {
      if (error.response?.status === 401 && route.name !== "login") {
        localStorage.removeItem("token");
        router.push({ name: "login" });
        return;
      }
      return Promise.reject(error);
    }
  );

  return {
    api,
  };
}
