import { useApiService } from '../api/apiService'
import type { LoginData, RegisterData } from '@/types/auth'

export function useAuthService() {
  const { api } = useApiService()

  const login = async (data: LoginData) => {
    try {
      const response = await api.post('/auth/login', data)
      return response.data
    } catch (error) {
      throw error
    }
  }

  const register = async (data: RegisterData) => {
    try {
      const response = await api.post('/auth/register', data)
      return response.data
    } catch (error) {
      throw error
    }
  }

  const logout = async () => {
    try {
      await api.post('/logout')
      localStorage.removeItem('token')
    } catch (error) {
      throw error
    }
  }

  const resetPassword = async (email: string) => {
    try {
      await api.post('/auth/reset-password', { email })
    } catch (error) {
      throw error
    }
  }

  return { login, register, logout, resetPassword }
}
