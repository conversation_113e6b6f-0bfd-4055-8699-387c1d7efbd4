import { defineStore } from 'pinia'
import { ref } from 'vue'

interface LoaderContext {
  isLoading: boolean
}

interface LoaderState {
  contexts: Record<string, LoaderContext>
}

export const useLoaderStore = defineStore('loader', () => {
  const contexts = ref<LoaderState['contexts']>({})

  const startLoading = (context: string) => {
    contexts.value[context] = {
      isLoading: true,
    }
  }

  const stopLoading = (context: string) => {
    if (contexts.value[context]) {
      contexts.value[context].isLoading = false
    }
  }

  const isLoading = (context: string) => {
    return contexts.value[context]?.isLoading || false
  }

  return {
    startLoading,
    stopLoading,
    isLoading,
  }
})
