import { defineStore } from 'pinia'
import { ref } from 'vue'

type ToastType = 'success' | 'error' | 'warning'

interface ToastResponse {
  message?: string
  error?: string
}

interface ToastState {
  isVisible: boolean
  message: string
  type: ToastType
  timeout: number | null
}

export const useGlobalToastStore = defineStore('globalToast', () => {
  const state = ref<ToastState>({
    isVisible: false,
    message: '',
    type: 'success',
    timeout: null,
  })

  const getMessageFromResponse = (
    response: ToastResponse | string | undefined,
    defaultMessage: string,
  ): string => {
    if (!response) return defaultMessage

    if (typeof response === 'string') return response

    return response.message || response.error || defaultMessage
  }

  const showToast = (
    messageOrResponse: string | ToastResponse | undefined,
    type: ToastType = 'success',
    duration: number = 3000,
    defaultMessage: string = 'Operação realizada com sucesso!',
  ) => {
    // Limpa qualquer timeout existente
    if (state.value.timeout) {
      clearTimeout(state.value.timeout)
    }

    // Atualiza o estado
    state.value.message = getMessageFromResponse(messageOrResponse, defaultMessage)
    state.value.type = type
    state.value.isVisible = true

    // Configura o timeout para fechar automaticamente
    if (duration > 0) {
      state.value.timeout = window.setTimeout(() => {
        hideToast()
      }, duration)
    }
  }

  const hideToast = () => {
    state.value.isVisible = false
    state.value.message = ''
    if (state.value.timeout) {
      clearTimeout(state.value.timeout)
      state.value.timeout = null
    }
  }

  // Métodos de conveniência para diferentes tipos de toast
  const successToast = (
    messageOrResponse: string | ToastResponse | undefined,
    duration?: number,
    defaultMessage: string = 'Operação realizada com sucesso!',
  ) => {
    showToast(messageOrResponse, 'success', duration, defaultMessage)
  }

  const errorToast = (
    messageOrResponse: string | ToastResponse | undefined,
    duration?: number,
    defaultMessage: string = 'Ocorreu um erro inesperado.',
  ) => {
    showToast(messageOrResponse, 'error', duration, defaultMessage)
  }

  const warningToast = (
    messageOrResponse: string | ToastResponse | undefined,
    duration?: number,
    defaultMessage: string = 'Atenção!',
  ) => {
    showToast(messageOrResponse, 'warning', duration, defaultMessage)
  }

  return {
    state,
    showToast,
    hideToast,
    successToast,
    errorToast,
    warningToast,
  }
})
