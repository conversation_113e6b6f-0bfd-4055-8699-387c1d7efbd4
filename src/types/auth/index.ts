export type User = {
  id: string
  name: string
  email: string
}

export type LoginData = {
  email: string
  password: string
  remember_me: boolean
}

export type RegisterData = {
  // Campos comuns
  email: string
  phone: string
  password: string
  password_confirmation: string
  agreement_terms: boolean
  document: string
  document_type: 'cpf' | 'cnpj'

  // Campos específicos para PF
  first_name?: string
  last_name?: string

  // Campos específicos para PJ
  company_name?: string
  responsible_name?: string
}
