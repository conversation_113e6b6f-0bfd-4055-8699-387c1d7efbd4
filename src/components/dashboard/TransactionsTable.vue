<template>
  <div class="bg-[#1e2c4a]/80 text-white rounded-xl border border-sky-900/30 shadow-sm p-4">
    <div
      class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-4 sm:gap-0"
    >
      <h3 class="font-semibold">{{ title }}</h3>
      <div class="flex items-center space-x-2 w-full sm:w-auto">
        <div class="relative flex-grow sm:flex-grow-0">
          <input
            v-model="searchQuery"
            type="text"
            :placeholder="searchPlaceholder"
            class="pl-8 pr-2 py-1 text-sm rounded-md border border-sky-900/50 bg-[#111827] text-white w-full"
            @input="handleSearch"
          />
          <Icon
            icon="mdi:magnify"
            class="h-4 w-4 absolute left-2 top-1/2 transform -translate-y-1/2 text-blue-200"
          />
        </div>
        <button
          @click="showFilters = !showFilters"
          class="bg-[#1e2c4a] border border-sky-900/50 text-blue-200 px-3 py-1 text-sm rounded-md flex items-center"
        >
          <Icon icon="mdi:filter" class="mr-1 h-4 w-4" />
          {{ filterButtonText }}
        </button>
      </div>
    </div>

    <!-- Filtros (opcional) -->
    <div v-if="showFilters" class="mb-4 p-3 bg-[#111827]/70 rounded-md border border-sky-900/30">
      <div class="grid grid-cols-1 sm:grid-cols-3 gap-3">
        <div>
          <label class="text-sm text-blue-200 block mb-1">Status</label>
          <select
            v-model="filters.status"
            class="w-full p-1.5 text-sm rounded-md border border-sky-900/50 bg-[#111827] text-white"
          >
            <option value="">Todos</option>
            <option value="completed">Concluído</option>
            <option value="pending">Pendente</option>
            <option value="failed">Falha</option>
          </select>
        </div>
        <div>
          <label class="text-sm text-blue-200 block mb-1">Data Inicial</label>
          <input
            v-model="filters.startDate"
            type="date"
            class="w-full p-1.5 text-sm rounded-md border border-sky-900/50 bg-[#111827] text-white"
          />
        </div>
        <div>
          <label class="text-sm text-blue-200 block mb-1">Data Final</label>
          <input
            v-model="filters.endDate"
            type="date"
            class="w-full p-1.5 text-sm rounded-md border border-sky-900/50 bg-[#111827] text-white"
          />
        </div>
      </div>
      <div class="mt-3 flex justify-end">
        <button
          @click="applyFilters"
          class="bg-[#60a5fa] hover:bg-[#4299e1] text-white px-3 py-1 text-sm rounded-md flex items-center"
        >
          <Icon icon="mdi:check" class="mr-1 h-4 w-4" />
          Aplicar Filtros
        </button>
      </div>
    </div>

    <div class="overflow-x-auto">
      <table class="w-full">
        <thead>
          <tr class="border-b border-sky-900/30">
            <th
              v-for="column in columns"
              :key="column.key"
              :class="[
                'py-3 px-2 text-sm font-medium text-blue-200',
                column.align === 'right' ? 'text-right' : 'text-left',
              ]"
            >
              {{ column.label }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="transaction in paginatedTransactions"
            :key="transaction.id"
            class="border-b border-sky-900/30 hover:bg-[#111827]/50 transition-colors"
          >
            <td
              v-for="column in columns"
              :key="column.key"
              class="py-3 px-2 text-sm"
              :class="{ 'text-right': column.align === 'right' }"
            >
              <template v-if="column.key === 'status'">
                <span
                  :class="[
                    'inline-flex items-center px-2 py-0.5 rounded text-xs font-medium',
                    getStatusClass(transaction.status),
                  ]"
                >
                  <Icon :icon="getStatusIcon(transaction.status)" class="mr-1 h-3 w-3" />
                  {{ getStatusText(transaction.status) }}
                </span>
              </template>
              <template v-else-if="column.key === 'actions'">
                <button
                  @click="viewDetails(transaction)"
                  class="text-[#60a5fa] hover:text-[#4299e1] flex items-center"
                >
                  <Icon icon="mdi:eye" class="mr-1 h-4 w-4" />
                  {{ detailsButtonText }}
                </button>
              </template>
              <template v-else>
                {{ transaction[column.key] }}
              </template>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="flex flex-col sm:flex-row justify-between items-center mt-4 gap-3 sm:gap-0">
      <div class="text-sm text-blue-200">
        {{ paginationText }}
      </div>
      <div class="flex space-x-1">
        <button
          @click="prevPage"
          :disabled="currentPage === 1"
          :class="[
            'px-3 py-1 rounded-md border border-sky-900/50 text-sm flex items-center',
            currentPage === 1
              ? 'opacity-50 cursor-not-allowed'
              : 'bg-[#111827] text-white hover:bg-[#1e2c4a]',
          ]"
        >
          <Icon icon="mdi:chevron-left" class="mr-1 h-4 w-4" />
          {{ prevButtonText }}
        </button>
        <button
          v-for="page in displayedPages"
          :key="page"
          @click="goToPage(page)"
          :class="[
            'px-3 py-1 rounded-md text-sm',
            currentPage === page
              ? 'bg-[#60a5fa] text-white'
              : 'border border-sky-900/50 bg-[#111827] text-white hover:bg-[#1e2c4a]',
          ]"
        >
          {{ page }}
        </button>
        <button
          @click="nextPage"
          :disabled="currentPage === totalPages"
          :class="[
            'px-3 py-1 rounded-md border border-sky-900/50 text-sm flex items-center',
            currentPage === totalPages
              ? 'opacity-50 cursor-not-allowed'
              : 'bg-[#111827] text-white hover:bg-[#1e2c4a]',
          ]"
        >
          {{ nextButtonText }}
          <Icon icon="mdi:chevron-right" class="ml-1 h-4 w-4" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Icon } from '@iconify/vue'

const props = defineProps({
  title: {
    type: String,
    default: 'Transações Recentes',
  },
  transactions: {
    type: Array,
    default: () => [],
  },
  columns: {
    type: Array,
    default: () => [
      { key: 'id', label: 'ID' },
      { key: 'date', label: 'Data' },
      { key: 'recipient', label: 'Destinatário' },
      { key: 'amount', label: 'Valor' },
      { key: 'status', label: 'Status' },
      { key: 'actions', label: 'Ações', align: 'right' },
    ],
  },
  itemsPerPage: {
    type: Number,
    default: 5,
  },
  searchPlaceholder: {
    type: String,
    default: 'Buscar...',
  },
  filterButtonText: {
    type: String,
    default: 'Filtrar',
  },
  detailsButtonText: {
    type: String,
    default: 'Detalhes',
  },
  prevButtonText: {
    type: String,
    default: 'Anterior',
  },
  nextButtonText: {
    type: String,
    default: 'Próximo',
  },
})

const emit = defineEmits(['view-details'])

const searchQuery = ref('')
const currentPage = ref(1)
const showFilters = ref(false)
const filters = ref({
  status: '',
  startDate: '',
  endDate: '',
})

// Filtragem de transações
const filteredTransactions = computed(() => {
  let result = [...props.transactions]

  // Aplicar filtro de busca
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(
      (transaction) =>
        transaction.id.toLowerCase().includes(query) ||
        transaction.recipient.toLowerCase().includes(query) ||
        transaction.amount.toLowerCase().includes(query),
    )
  }

  // Aplicar filtros avançados
  if (filters.value.status) {
    result = result.filter((transaction) => transaction.status === filters.value.status)
  }

  if (filters.value.startDate) {
    const startDate = new Date(filters.value.startDate)
    result = result.filter((transaction) => {
      const transactionDate = new Date(transaction.dateRaw || transaction.date)
      return transactionDate >= startDate
    })
  }

  if (filters.value.endDate) {
    const endDate = new Date(filters.value.endDate)
    endDate.setHours(23, 59, 59)
    result = result.filter((transaction) => {
      const transactionDate = new Date(transaction.dateRaw || transaction.date)
      return transactionDate <= endDate
    })
  }

  return result
})

// Paginação
const totalItems = computed(() => filteredTransactions.value.length)
const totalPages = computed(() => Math.ceil(totalItems.value / props.itemsPerPage))

const paginatedTransactions = computed(() => {
  const start = (currentPage.value - 1) * props.itemsPerPage
  const end = start + props.itemsPerPage
  return filteredTransactions.value.slice(start, end)
})

const displayedPages = computed(() => {
  const pages = []
  const maxPagesToShow = 3

  if (totalPages.value <= maxPagesToShow) {
    // Mostrar todas as páginas se houver poucas
    for (let i = 1; i <= totalPages.value; i++) {
      pages.push(i)
    }
  } else {
    // Lógica para mostrar páginas ao redor da página atual
    let startPage = Math.max(1, currentPage.value - 1)
    const endPage = Math.min(totalPages.value, startPage + maxPagesToShow - 1)

    // Ajustar se estamos no final
    if (endPage === totalPages.value) {
      startPage = Math.max(1, endPage - maxPagesToShow + 1)
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i)
    }
  }

  return pages
})

const paginationText = computed(() => {
  const start = (currentPage.value - 1) * props.itemsPerPage + 1
  const end = Math.min(start + props.itemsPerPage - 1, totalItems.value)
  return `Mostrando ${start}-${end} de ${totalItems.value} transações`
})

// Métodos
const handleSearch = () => {
  currentPage.value = 1 // Voltar para a primeira página ao buscar
}

const applyFilters = () => {
  currentPage.value = 1 // Voltar para a primeira página ao filtrar
  showFilters.value = false // Fechar o painel de filtros
}

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}

const goToPage = (page) => {
  currentPage.value = page
}

const viewDetails = (transaction) => {
  emit('view-details', transaction)
}

// Funções auxiliares para status
const getStatusClass = (status) => {
  switch (status) {
    case 'completed':
      return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-400'
    case 'pending':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
    case 'failed':
      return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-800/30 dark:text-gray-400'
  }
}

const getStatusText = (status) => {
  switch (status) {
    case 'completed':
      return 'Concluído'
    case 'pending':
      return 'Pendente'
    case 'failed':
      return 'Falha'
    default:
      return status
  }
}

// Function to get status icon
const getStatusIcon = (status: string) => {
  switch (status.toLowerCase()) {
    case 'concluído':
      return 'mdi:check-circle'
    case 'pendente':
      return 'mdi:clock-outline'
    case 'falha':
      return 'mdi:alert-circle'
    default:
      return 'mdi:information'
  }
}

// Reset para a primeira página quando os filtros mudam
watch(
  [searchQuery, filters],
  () => {
    currentPage.value = 1
  },
  { deep: true },
)
</script>
