<template>
  <div class="bg-card text-card-foreground rounded-xl border shadow-sm p-4">
    <div class="flex justify-between items-start">
      <div>
        <p class="text-muted-foreground text-sm">{{ title }}</p>
        <h3 class="text-2xl font-bold mt-1">{{ value }}</h3>
        <p
          :class="[
            'text-xs mt-1 flex items-center',
            trend === 'up'
              ? 'text-emerald-500'
              : trend === 'down'
                ? 'text-red-500'
                : 'text-yellow-500',
          ]"
        >
          <Icon
            v-if="trend !== 'neutral'"
            :icon="trend === 'up' ? 'mdi:arrow-up' : 'mdi:arrow-down'"
            class="h-3 w-3 mr-1"
          />
          <Icon v-else icon="mdi:minus" class="h-3 w-3 mr-1" />
          {{ change }} {{ trend !== 'neutral' ? 'este mês' : 'mesmo que ontem' }}
        </p>
      </div>
      <div :class="['p-2 rounded-lg', `bg-${color}-100 dark:bg-${color}-900/30`]">
        <Icon
          v-if="iconType === 'transaction'"
          icon="mdi:swap-horizontal"
          :class="`h-6 w-6 text-${color}-600 dark:text-${color}-400`"
        />
        <Icon
          v-else-if="iconType === 'success'"
          icon="mdi:check-bold"
          :class="`h-6 w-6 text-${color}-600 dark:text-${color}-400`"
        />
        <Icon
          v-else-if="iconType === 'pending'"
          icon="mdi:clock-outline"
          :class="`h-6 w-6 text-${color}-600 dark:text-${color}-400`"
        />
        <Icon
          v-else-if="iconType === 'failed'"
          icon="mdi:close"
          :class="`h-6 w-6 text-${color}-600 dark:text-${color}-400`"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'

defineProps<{
  title: string
  value: string
  change: string
  trend: 'up' | 'down' | 'neutral'
  iconType: 'transaction' | 'success' | 'pending' | 'failed'
  color: string
}>()
</script>
