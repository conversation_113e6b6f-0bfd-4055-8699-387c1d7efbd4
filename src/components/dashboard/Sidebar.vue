<template>
  <div>
    <!-- Overlay para quando o sidebar estiver aberto em dispositivos móveis -->
    <div
      v-if="isOpen"
      class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-20"
      @click="toggleSidebar"
    ></div>

    <aside
      :class="[
        'bg-[#1e2c4a] text-white p-4 transition-all duration-300 ease-in-out z-30',
        'fixed md:static top-0 bottom-0 left-0',
        isOpen ? 'translate-x-0 w-64' : '-translate-x-full md:translate-x-0 w-0 md:w-20',
        'md:min-h-screen overflow-hidden',
      ]"
    >
      <div class="flex items-center justify-between mb-8">
        <div class="flex justify-center items-center gap-2">
          <img src="@/assets/logo-light.svg" alt="Logo" class="w-12 h-12" />
          <h1 class="text-xl font-bold whitespace-nowrap overflow-hidden" v-show="isOpen">
            Global
          </h1>
        </div>
        <!-- Botão de toggle é removido em desktop (md e acima) -->
        <button @click="toggleSidebar" class="text-white md:hidden">
          <Icon :icon="isOpen ? 'mdi:close' : 'mdi:menu'" class="h-6 w-6" />
        </button>
      </div>

      <nav class="space-y-2 overflow-y-auto">
        <a
          v-for="(item, index) in menuItems"
          :key="index"
          :href="item.url"
          :class="[
            'flex items-center rounded-lg transition-colors',
            item.active ? 'bg-[#60a5fa] text-white' : 'hover:bg-[#111827]/50',
            isOpen ? 'p-2' : 'p-2 md:justify-center',
          ]"
        >
          <Icon :icon="item.icon" class="h-6 w-6 flex-shrink-0" />
          <span v-show="isOpen" class="ml-2 whitespace-nowrap">{{ item.label }}</span>
        </a>
      </nav>
    </aside>

    <!-- Botão flutuante para abrir o sidebar em dispositivos móveis quando fechado -->
    <button
      v-if="!isOpen"
      @click="toggleSidebar"
      class="md:hidden fixed top-4 left-4 bg-[#60a5fa] hover:bg-[#4299e1] text-white p-2 rounded-md shadow-lg z-20"
    >
      <Icon icon="mdi:menu" class="h-6 w-6" />
    </button>
  </div>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'

defineProps<{
  isOpen: boolean
}>()

const emit = defineEmits(['toggle'])

const toggleSidebar = () => {
  emit('toggle')
}

// Menu items com ícones do Iconify
const menuItems = [
  {
    label: 'Dashboard',
    url: '#',
    active: true,
    icon: 'mdi:view-dashboard',
  },
  {
    label: 'Transações',
    url: '#',
    active: false,
    icon: 'mdi:swap-horizontal',
  },
  {
    label: 'Recebimentos',
    url: '#',
    active: false,
    icon: 'mdi:arrow-down-bold',
  },
  {
    label: 'Chaves PIX',
    url: '#',
    active: false,
    icon: 'mdi:key-variant',
  },
  {
    label: 'Suporte',
    url: '#',
    active: false,
    icon: 'mdi:help-circle',
  },
  {
    label: 'Perfil',
    url: '#',
    active: false,
    icon: 'mdi:account',
  },
]
</script>

<script lang="ts">
export default {
  name: 'DashboardSidebar',
}
</script>
