<template>
  <div class="h-64">
    <apexchart type="bar" height="100%" :options="chartOptions" :series="series"></apexchart>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// Dias da semana em português
const weekDays = ['Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb', 'Dom']

// Opções do gráfico
const chartOptions = ref({
  chart: {
    type: 'bar',
    height: 350,
    toolbar: {
      show: false,
    },
    fontFamily: 'Inter, sans-serif',
  },
  plotOptions: {
    bar: {
      borderRadius: 4,
      columnWidth: '60%',
      colors: {
        ranges: [
          {
            from: 0,
            to: 100,
            color: '#3b82f6', // Azul padrão para todas as barras
          },
        ],
      },
    },
  },
  dataLabels: {
    enabled: false,
  },
  xaxis: {
    categories: weekDays,
    axisBorder: {
      show: false,
    },
    axisTicks: {
      show: false,
    },
    labels: {
      style: {
        colors: '#64748b', // Cor do texto nos rótulos do eixo X
        fontSize: '12px',
      },
    },
  },
  yaxis: {
    title: {
      text: 'Transações',
      style: {
        color: '#64748b',
        fontSize: '12px',
        fontWeight: 500,
      },
    },
    labels: {
      style: {
        colors: '#64748b', // Cor do texto nos rótulos do eixo Y
        fontSize: '12px',
      },
    },
  },
  grid: {
    borderColor: '#e2e8f0',
    strokeDashArray: 4,
    yaxis: {
      lines: {
        show: true,
      },
    },
    xaxis: {
      lines: {
        show: false,
      },
    },
  },
  fill: {
    opacity: 0.8,
    colors: ['#3b82f6'],
  },
  tooltip: {
    y: {
      formatter: (val: number) => `${val} transações`,
    },
    theme: 'light',
    style: {
      fontSize: '12px',
      fontFamily: 'Inter, sans-serif',
    },
  },
  responsive: [
    {
      breakpoint: 640,
      options: {
        plotOptions: {
          bar: {
            columnWidth: '80%',
          },
        },
        yaxis: {
          labels: {
            show: false,
          },
        },
      },
    },
  ],
})

// Dados das séries
const series = ref([
  {
    name: 'Transações',
    data: [30, 45, 68, 87, 56, 74, 98],
  },
])

onMounted(() => {
  // Aqui poderíamos carregar os dados reais de uma API
  // fetchTransactionData();
})

// Função para buscar dados (mock)
/*
const fetchTransactionData = async () => {
  try {
    // Simulando uma chamada de API
    // const response = await api.get('/transactions/weekly');
    // series.value[0].data = response.data;

    // Por enquanto usamos dados simulados
    setTimeout(() => {
      series.value = [
        {
          name: 'Transações',
          data: [42, 53, 67, 91, 78, 63, 84]
        }
      ];
    }, 1000);
  } catch (error) {
    console.error('Erro ao buscar dados do gráfico:', error);
  }
};
*/
</script>
