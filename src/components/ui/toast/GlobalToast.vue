<template>
  <div
    class="flex items-center w-full max-w-xs p-4 mb-8 mr-1 sm:mr-0 sm:mb-4 text-gray-500 rounded-lg shadow-sm dark:text-gray-400 dark:bg-gray-800"
    role="alert"
    :class="[
      type === 'success' && 'bg-green-500',
      type === 'error' && 'bg-red-500',
      type === 'warning' && 'bg-amber-500',
    ]"
  >
    <div class="ms-3 text-sm font-normal text-white">{{ message }}</div>
    <button
      type="button"
      class="ms-auto -mx-1.5 -my-1.5 text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 hover:cursor-pointer inline-flex items-center justify-center h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700"
      @click="$emit('close')"
      aria-label="Close"
    >
      <span class="sr-only">Close</span>
      <Icon
        @click="$emit('close')"
        icon="mdi:close"
        class="w-6 h-6 text-white"
        :class="[
          type === 'success' && 'hover:text-green-500',
          type === 'error' && 'hover:text-red-500',
          type === 'warning' && 'hover:text-amber-500',
        ]"
      />
    </button>
  </div>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'

defineProps<{
  type: 'success' | 'error' | 'warning'
  message: string
}>()

defineEmits<{
  (e: 'close'): void
}>()
</script>
