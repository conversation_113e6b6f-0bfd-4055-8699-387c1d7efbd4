<!-- Componente BackToTopButton.vue -->
<template>
  <transition name="fade">
    <button v-show="isVisible" @click="scrollToTop"
      class="fixed cursor-n-resize w-[50px] bottom-6 right-6 z-50 bg-cyan-900 hover:bg-sky-700 text-white p-3 rounded-full shadow-lg transition-all duration-300 transform hover:scale-105 focus:outline-none"
      aria-label="Voltar ao topo">
      <i class="fas fa-arrow-up"></i>
    </button>
  </transition>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';

const isVisible = ref(false);

const checkScroll = (): void => {
  isVisible.value = window.scrollY > 300;
};

const scrollToTop = (): void => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  });
};

onMounted(() => {
  window.addEventListener('scroll', checkScroll);
});

onUnmounted(() => {
  window.removeEventListener('scroll', checkScroll);
});
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s, transform 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}
</style>

