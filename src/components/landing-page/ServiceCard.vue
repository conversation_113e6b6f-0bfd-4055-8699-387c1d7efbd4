<template>
  <div class="bg-[#1e2c4a] p-6 sm:p-8 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 hover:translate-y-[-5px] h-full flex flex-col">
    <div class="w-14 h-14 bg-[#0c4a6e] rounded-lg flex items-center justify-center mb-6 transform transition-transform hover:rotate-3">
      <i :class="['fas', icon, 'text-xl', 'text-blue-400']"></i>
    </div>
    <h3 class="text-xl font-bold mb-4 text-white">{{ title }}</h3>
    <p class="text-blue-200 text-sm sm:text-base mb-6 flex-grow">
      {{ description }}
    </p>
  </div>
</template>

<script setup lang="ts">
interface Props {
  icon: string;
  title: string;
  description: string;
}

defineProps<Props>();
</script>

