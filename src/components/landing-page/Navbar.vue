<template>
  <nav
    :class="[
      'fixed top-0 left-0 right-0 z-50 w-full py-4 transition-all duration-300',
      { 'bg-transparent': !scrolled, 'bg-[#111827] shadow-lg': scrolled },
    ]"
  >
    <div class="container mx-auto px-8">
      <div class="flex justify-between items-center">
        <a
          href="#hero"
          class="flex justify-center items-center gap-2 font-bold text-white text-lg z-20 relative p-2"
        >
          <img src="@/assets/logo-light.svg" alt="Logo" class="h-8" />
          <span>Global</span>
        </a>

        <div class="hidden md:flex space-x-8 text-sm">
          <a href="#about" class="p-2 text-white hover:text-blue-200 transition-colors">Sobre</a>
          <a href="#services" class="p-2 text-white hover:text-blue-200 transition-colors"
            >Como funciona</a
          >
          <RouterLink
            class="border-b border-cyan-950 p-2 rounded hover:border-b hover:border-sky-800 text-white hover:text-blue-200"
            to="/auth/register"
            >Criar Conta</RouterLink
          >
          <RouterLink
            class="border-b border-cyan-950 p-2 rounded hover:border-b hover:border-sky-800 text-white hover:text-blue-200"
            to="/auth/login"
            >Acessar</RouterLink
          >
        </div>

        <button
          @click="toggleMobileMenu"
          class="md:hidden z-20 relative focus:outline-none"
          aria-label="Toggle menu"
        >
          <div class="flex items-center justify-center">
            <i v-if="!mobileMenuOpen" class="fas fa-bars text-xl text-white"></i>
            <i v-else class="fas fa-times text-xl text-white"></i>
          </div>
        </button>
      </div>
    </div>

    <div class="md:hidden">
      <transition name="fade">
        <div
          v-if="mobileMenuOpen"
          class="fixed inset-0 bg-[#111827] bg-opacity-50 z-10"
          @click="toggleMobileMenu"
        ></div>
      </transition>

      <transition name="slide">
        <div
          v-if="mobileMenuOpen"
          class="fixed top-0 right-0 h-full w-[100%] bg-[#111827] z-20 flex flex-col p-8 shadow-lg"
        >
          <div class="flex justify-between items-center mb-8">
            <div class="font-bold text-white text-lg">Menu</div>
            <button
              @click="toggleMobileMenu"
              class="text-white hover:text-blue-200 transition-colors duration-300"
            >
              <i class="fas fa-times text-xl"></i>
            </button>
          </div>

          <div class="flex flex-col space-y-6 text-white text-lg">
            <a
              href="#about"
              @click="toggleMobileMenu"
              class="hover:text-blue-200 transition-colors duration-300 py-2 border-b border-gray-700"
              >Sobre</a
            >
            <a
              href="#services"
              @click="toggleMobileMenu"
              class="hover:text-blue-200 transition-colors duration-300 py-2 border-b border-gray-700"
              >Como funciona</a
            >
            <RouterLink
              @click="toggleMobileMenu"
              class="hover:text-blue-200 transition-colors duration-300 py-2 border-b border-gray-700"
              to="/auth/register"
              >Criar Conta</RouterLink
            >
            <RouterLink
              @click="toggleMobileMenu"
              class="hover:text-blue-200 transition-colors duration-300 py-2 border-b border-gray-700"
              to="/auth/login"
            >
              Acessar</RouterLink
            >
          </div>
        </div>
      </transition>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const mobileMenuOpen = ref(false)
const scrolled = ref(false)

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value

  if (mobileMenuOpen.value) {
    document.body.style.overflow = 'hidden'
  } else {
    document.body.style.overflow = ''
  }
}

const handleScroll = () => {
  if (window.scrollY > 50) {
    scrolled.value = true
  } else {
    scrolled.value = false
  }
}

onMounted(() => {
  const handleResize = () => {
    if (window.innerWidth >= 768 && mobileMenuOpen.value) {
      mobileMenuOpen.value = false
      document.body.style.overflow = ''
    }
  }

  window.addEventListener('resize', handleResize)
  window.addEventListener('scroll', handleScroll)

  handleScroll()

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    window.removeEventListener('scroll', handleScroll)
  })
})
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.4s cubic-bezier(0.19, 1, 0.22, 1);
}

.slide-enter-from,
.slide-leave-to {
  transform: translateX(100%);
}
</style>
