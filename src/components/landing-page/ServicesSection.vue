<template>
  <section id="services"
    class="-mb-2 py-20 min-h-screen flex flex-col justify-center full-width relative overflow-hidden">
    <div class="absolute top-20 left-10 w-40 h-40 rounded-full bg-blue-500 opacity-5"></div>
    <div class="absolute bottom-20 right-10 w-64 h-64 rounded-full bg-blue-300 opacity-5"></div>

    <div class="container mx-auto px-4 md:px-8 flex-grow flex flex-col justify-center relative z-10">
      <div class="text-center max-w-3xl mx-auto mb-16">
        <h2 class="text-2xl sm:text-3xl font-bold text-white mb-4">Entenda o fluxo de integração</h2>
        <p class="text-blue-200 text-base">Solução completa para processamento de pagamentos PIX: da contratação à
          intermediação financeira</p>
      </div>

      <div class="grid grid-cols-1 sm:grid-cols-3 gap-8 sm:gap-6 md:gap-8 lg:max-w-5xl lg:mx-auto xl:max-w-6xl">
        <div class="transform hover:-translate-y-2 transition-transform duration-300">
          <ServiceCard icon="fa-handshake" title="Contratação Simplificada"
            description="Processo de onboarding rápido e seguro. Cadastre sua empresa, envie seus documentos e tenha acesso à nossa plataforma de pagamentos PIX em poucos passos, sem burocracia." />
        </div>

        <div class="transform hover:-translate-y-2 transition-transform duration-300">
          <ServiceCard icon="fa-plug" title="Integração Tecnológica"
            description="Implementação técnica flexível via API REST. Nossa equipe oferece suporte completo para garantir que sua plataforma esteja conectada e operando em tempo recorde." />
        </div>

        <div class="transform hover:-translate-y-2 transition-transform duration-300">
          <ServiceCard icon="fa-exchange-alt" title="Gestão Simplificada"
            description="Gestão completa do fluxo transacional com total transparência. Tenha controle e acesso total sobre o ciclo de pagamentos via dashboard." />
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import ServiceCard from './ServiceCard.vue';
</script>

<style scoped>
@media (min-width: 1024px) and (max-width: 1440px) {
  .grid {
    gap: 1.5rem;
  }
}
</style>
