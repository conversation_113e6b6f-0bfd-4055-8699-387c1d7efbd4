<template>
  <div class="min-h-screen">
    <HeroSection />
    <AboutSection />
    <ServicesSection />
    <CtaSection />
    <BackToTopButton />
  </div>
</template>

<script setup lang="ts">
import HeroSection from '@/components/landing-page/HeroSection.vue'
import AboutSection from '@/components/landing-page/AboutSection.vue'
import ServicesSection from '@/components/landing-page/ServicesSection.vue'
import CtaSection from '@/components/landing-page/CtaSection.vue'
import BackToTopButton from '@/components/landing-page/BackToTopButton.vue'

import '@/assets/styles.css'
</script>

<style>
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css');

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

html {
  scroll-behavior: smooth;
}

@media (min-width: 1024px) and (max-width: 1440px) {
  .container {
    max-width: 90%;
    margin-left: auto;
    margin-right: auto;
  }
}

@media (max-width: 640px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}
</style>
