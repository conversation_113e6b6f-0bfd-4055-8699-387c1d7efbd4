<template>
  <div class="flex h-screen bg-[#111827]">
    <!-- Sidebar -->
    <Sidebar :is-open="isSidebarOpen" @toggle="toggleSidebar" class="h-screen flex-shrink-0" />

    <!-- Container principal (header + conteúdo) -->
    <div class="flex flex-col flex-1 min-w-0">
      <!-- Header -->
      <header class="bg-[#111827] shadow-md p-3 border-b border-blue-900/30">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <button
              @click="toggleSidebar"
              class="p-2 rounded-lg text-white hover:bg-blue-800/30 mr-3"
            >
              <Icon icon="lucide:indent-increase" class="h-6 w-6" v-if="!isSidebarOpen" />
              <Icon icon="lucide:indent-decrease" class="h-6 w-6" v-else />
            </button>
          </div>
          <div class="text-white"></div>
        </div>
      </header>

      <!-- Main Content -->
      <main class="flex-1 overflow-auto p-3 md:p-5 lg:p-6">
        <div class="mb-6">
          <h1 class="text-2xl md:text-3xl font-bold text-white">Dashboard de Transações</h1>
          <p class="text-blue-200 mt-1">Gerencie e monitore suas transações PIX em tempo real</p>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-3 md:gap-4 mb-6">
          <StatCard
            title="Total de Transações"
            value="1,248"
            change="12%"
            trend="up"
            icon-type="transaction"
            color="blue"
          />
          <StatCard
            title="Transações Concluídas"
            value="1,180"
            change="8%"
            trend="up"
            icon-type="success"
            color="emerald"
          />
          <StatCard
            title="Transações Pendentes"
            value="42"
            change="0%"
            trend="neutral"
            icon-type="pending"
            color="yellow"
          />
          <StatCard
            title="Transações Falhas"
            value="26"
            change="2%"
            trend="down"
            icon-type="failed"
            color="red"
          />
        </div>

        <!-- Chart and Transactions Section -->
        <div class="grid grid-cols-1 xl:grid-cols-2 gap-4 md:gap-5">
          <!-- Recent Transactions -->
          <TransactionsTable
            :transactions="transactions"
            :total-transactions="1248"
            :current-page="currentPage"
            @page-change="handlePageChange"
          />

          <!-- Chart -->
          <div class="bg-[#1e2c4a]/80 text-white rounded-xl border border-sky-900/30 shadow p-4">
            <div class="pb-3 mb-3 flex justify-between items-center">
              <h3 class="font-semibold text-lg">Visão Geral de Transações</h3>
            </div>
            <div class="flex justify-center space-x-2 mb-4">
              <button
                class="text-xs bg-[#60a5fa] hover:bg-[#3b82f6] text-white px-3 py-1 rounded-md transition-colors"
              >
                Diário
              </button>
              <button
                class="text-xs text-blue-200 hover:bg-blue-800/50 px-3 py-1 rounded-md transition-colors"
              >
                Semanal
              </button>
              <button
                class="text-xs text-blue-200 hover:bg-blue-800/50 px-3 py-1 rounded-md transition-colors"
              >
                Mensal
              </button>
            </div>
            <TransactionsChart />
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import Sidebar from '@/components/dashboard/Sidebar.vue'
import StatCard from '@/components/dashboard/StatCard.vue'
import TransactionsChart from '@/components/dashboard/TransactionsChart.vue'
import TransactionsTable from '@/components/dashboard/TransactionsTable.vue'
import { Icon } from '@iconify/vue'

// Estado do sidebar - fechado por padrão em mobile, aberto em desktop
const isSidebarOpen = ref(window.innerWidth >= 1024)

// Função para verificar o tamanho da tela e ajustar o sidebar
const checkScreenSize = () => {
  isSidebarOpen.value = window.innerWidth >= 1024
}

// Toggle sidebar
const toggleSidebar = () => {
  isSidebarOpen.value = !isSidebarOpen.value
}

// Paginação
const currentPage = ref(1)
const handlePageChange = (page: number) => {
  currentPage.value = page
  // Aqui você pode carregar os dados da nova página
}

// Dados mock para transações
const transactions = ref([
  {
    id: '#PIX12345',
    date: '15/06/2023 14:30',
    recipient: 'João Silva',
    amount: 'R$ 1.250,00',
    status: 'concluído',
  },
  {
    id: '#PIX12346',
    date: '15/06/2023 15:45',
    recipient: 'Maria Oliveira',
    amount: 'R$ 750,00',
    status: 'concluído',
  },
  {
    id: '#PIX12347',
    date: '15/06/2023 16:20',
    recipient: 'Carlos Mendes',
    amount: 'R$ 2.500,00',
    status: 'pendente',
  },
  {
    id: '#PIX12348',
    date: '15/06/2023 17:05',
    recipient: 'Ana Souza',
    amount: 'R$ 1.800,00',
    status: 'falha',
  },
  {
    id: '#PIX12349',
    date: '15/06/2023 18:30',
    recipient: 'Pedro Santos',
    amount: 'R$ 950,00',
    status: 'concluído',
  },
])

onMounted(() => {
  // Adicionar listener para ajustar o sidebar quando a janela for redimensionada
  window.addEventListener('resize', checkScreenSize)

  // Verificar tamanho da tela no carregamento inicial
  checkScreenSize()

  // Aqui você pode carregar dados reais da API quando o componente for montado
  // fetchTransactionsData()

  // Remover listener quando o componente for desmontado
  return () => {
    window.removeEventListener('resize', checkScreenSize)
  }
})

// Função para buscar dados reais (a ser implementada)
// const fetchTransactionsData = async () => {
//   try {
//     // const response = await api.get('/transactions')
//     // transactions.value = response.data
//   } catch (error) {
//     console.error('Erro ao buscar dados:', error)
//   }
// }
</script>

<style scoped>
/* Garantir que o sidebar tenha altura total em mobile */
@media (max-width: 1023px) {
  .sidebar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 50;
  }
}
</style>
