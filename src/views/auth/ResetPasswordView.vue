<template>
  <div class="min-h-screen overflow-x-hidden bg-[#111827] flex flex-col">
    <div
      class="relative overflow-hidden flex-grow flex items-center justify-center min-h-[calc(100vh-80px)]"
    >
      <div class="absolute w-full h-full">
        <div class="absolute inset-0 bg-[#111827] opacity-70"></div>
        <img
          src="@/assets/auth/globo-auth.jpg"
          alt="Background"
          class="absolute inset-0 w-full h-full object-cover opacity-30"
        />
      </div>

      <div class="container mx-auto px-4 md:px-8 relative z-10 py-12">
        <div class="max-w-md mx-auto">
          <div
            class="bg-[#1e2c4a]/80 backdrop-blur-sm rounded-xl shadow-lg overflow-hidden border border-sky-900/30"
          >
            <div class="p-6 sm:p-8 border-b border-sky-900/30">
              <h2 class="text-2xl sm:text-3xl font-bold text-white mb-2">Recuperar <PERSON></h2>
              <p class="text-blue-200 text-sm sm:text-base">
                Informe seu email para receber um link de redefinição de senha
              </p>
            </div>

            <div class="p-6 sm:p-8">
              <form @submit.prevent="handleResetPassword" class="space-y-5 animate-form">
                <div>
                  <label class="block text-white text-sm font-medium mb-2" for="email">
                    Email
                  </label>
                  <div class="relative">
                    <div
                      class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                    >
                      <Icon icon="mdi:email" class="w-5 h-5 text-blue-400" />
                    </div>
                    <input
                      v-model="email"
                      @blur="validateField('email', email)"
                      @input="clearFieldError('email')"
                      class="w-full bg-[#111827]/80 text-white border rounded-lg py-3 pl-10 pr-4 focus:outline-none focus:border-[#60a5fa] focus:ring-1 focus:ring-[#60a5fa] transition-colors"
                      :class="{
                        'border-red-500': errors.email,
                        'border-sky-900/50': !errors.email,
                      }"
                      type="email"
                      id="email"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <p v-if="errors.email" class="mt-1 text-sm text-red-500">{{ errors.email }}</p>
                </div>

                <button
                  type="submit"
                  class="w-full bg-[#60a5fa] hover:bg-[#4299e1] text-white py-3 px-4 rounded-lg font-medium transition-colors duration-300 flex items-center justify-center"
                  :disabled="isLoading"
                >
                  <Icon v-if="isLoading" icon="svg-spinners:ring-resize" class="w-6 h-6" />
                  <div v-else class="flex items-center gap-2">
                    <span>Enviar link de recuperação</span>
                    <Icon icon="mdi:send" class="w-5 h-5" />
                  </div>
                </button>
              </form>

              <div class="mt-8 text-center">
                <p class="text-blue-200 text-sm">
                  Lembrou sua senha?
                  <RouterLink
                    to="/auth/login"
                    class="text-[#60a5fa] hover:text-[#4299e1] font-medium ml-1 transition-colors"
                  >
                    Voltar para o login
                  </RouterLink>
                </p>
              </div>
            </div>
          </div>

          <div class="mt-8 text-center">
            <p class="text-blue-200 text-xs">
              © GLOBAL Pagamentos © {{ new Date().getFullYear() }} - Todos os direitos reservados
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useGlobalToastStore } from '@/stores/global-toast'
import { Icon } from '@iconify/vue'
import * as yup from 'yup'

const router = useRouter()
const { successToast, errorToast } = useGlobalToastStore()

const email = ref('')
const isLoading = ref(false)
const hasAttemptedSubmit = ref(false)

const errors = reactive({
  email: '',
})

const validationSchema = yup.object().shape({
  email: yup.string().email('Por favor, insira um email válido').required('O email é obrigatório'),
})

const validateField = async (field: string, value: string) => {
  // Só valida se já tentou submeter ou se o campo tem algum valor
  if (!hasAttemptedSubmit.value && !value) {
    errors[field] = ''
    return
  }

  try {
    await validationSchema.validateAt(field, { [field]: value })
    errors[field] = ''
  } catch (err) {
    if (err instanceof yup.ValidationError) {
      // Se não tentou submeter ainda, só mostra erro de formato, não de campo obrigatório
      if (!hasAttemptedSubmit.value && err.message.includes('obrigatório')) {
        errors[field] = ''
      } else {
        errors[field] = err.message
      }
    }
  }
}

const clearFieldError = (field: string) => {
  errors[field] = ''
}

const handleResetPassword = async () => {
  hasAttemptedSubmit.value = true
  isLoading.value = true

  try {
    await validationSchema.validate({ email: email.value }, { abortEarly: false })

    // Aqui você deve implementar a chamada real para a API
    // Por enquanto, vamos simular um delay
    await new Promise((resolve) => setTimeout(resolve, 1000))

    successToast('Email enviado', `Um link de recuperação de senha foi enviado para ${email.value}`)
    router.push('/auth/login')
  } catch (error) {
    if (error instanceof yup.ValidationError) {
      error.inner.forEach((err) => {
        if (err.path) {
          errors[err.path] = err.message
        }
      })
    } else {
      errorToast(
        'Erro ao enviar email',
        'Ocorreu um erro ao enviar o email de recuperação. Por favor, tente novamente.',
      )
    }
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
@keyframes formFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-form {
  animation: formFadeIn 0.6s ease-out forwards;
}

input:hover {
  border-color: rgba(96, 165, 250, 0.5);
}

input.border-red-500:hover {
  border-color: rgb(239, 68, 68);
}

@media (max-width: 640px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}
</style>
