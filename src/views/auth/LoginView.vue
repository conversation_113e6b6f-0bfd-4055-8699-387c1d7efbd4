<template>
  <div class="min-h-screen overflow-x-hidden bg-[#111827] flex flex-col">
    <div
      class="relative overflow-hidden flex-grow flex items-center justify-center min-h-[calc(100vh-80px)]"
    >
      <div class="absolute w-full h-full">
        <div class="absolute inset-0 bg-[#111827] opacity-70"></div>
        <img
          src="@/assets/auth/globo-auth.jpg"
          alt="Background"
          class="absolute inset-0 w-full h-full object-cover opacity-30"
        />
      </div>

      <div class="container mx-auto px-4 md:px-8 relative z-10 py-12">
        <div class="max-w-md mx-auto">
          <div
            class="bg-[#1e2c4a]/80 backdrop-blur-sm rounded-xl shadow-lg overflow-hidden border border-sky-900/30"
          >
            <div class="p-6 sm:p-8 border-b border-sky-900/30">
              <h2 class="text-2xl sm:text-3xl font-bold text-white mb-2">Bem-vindo de volta</h2>
              <p class="text-blue-200 text-sm sm:text-base">
                Acesse sua conta para gerenciar seus pagamentos
              </p>
            </div>

            <div class="p-6 sm:p-8">
              <form @submit.prevent="handle_login" class="space-y-5 animate-form">
                <div>
                  <label class="block text-white text-sm font-medium mb-2" for="email">
                    Email
                  </label>
                  <div class="relative">
                    <div
                      class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                    >
                      <Icon icon="mdi:email" class="w-5 h-5 text-blue-400" />
                    </div>
                    <input
                      v-model="email"
                      @blur="validateField('email', email)"
                      @input="clearFieldError('email')"
                      class="w-full bg-[#111827]/80 text-white border rounded-lg py-3 pl-10 pr-4 focus:outline-none focus:border-[#60a5fa] focus:ring-1 focus:ring-[#60a5fa] transition-colors"
                      :class="{
                        'border-red-500': errors.email,
                        'border-sky-900/50': !errors.email,
                      }"
                      type="email"
                      id="email"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <p v-if="errors.email" class="mt-1 text-sm text-red-500">{{ errors.email }}</p>
                </div>

                <div>
                  <div class="flex justify-between items-center mb-2">
                    <label class="block text-white text-sm font-medium" for="password">
                      Senha
                    </label>
                    <RouterLink
                      to="/auth/reset-password"
                      class="text-blue-400 text-xs hover:text-blue-300 transition-colors"
                    >
                      Esqueceu sua senha?
                    </RouterLink>
                  </div>
                  <div class="relative">
                    <div
                      class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                    >
                      <Icon icon="mdi:lock" class="w-5 h-5 text-blue-400" />
                    </div>
                    <input
                      v-model="password"
                      @blur="validateField('password', password)"
                      @input="clearFieldError('password')"
                      class="w-full bg-[#111827]/80 text-white border rounded-lg py-3 pl-10 pr-4 focus:outline-none focus:border-[#60a5fa] focus:ring-1 focus:ring-[#60a5fa] transition-colors"
                      :class="{
                        'border-red-500': errors.password,
                        'border-sky-900/50': !errors.password,
                      }"
                      :type="show_password ? 'text' : 'password'"
                      id="password"
                      placeholder="••••••••"
                    />
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                      <button
                        type="button"
                        @click="toggle_password"
                        class="text-blue-400 hover:text-blue-300 focus:outline-none"
                      >
                        <i :class="show_password ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                      </button>
                    </div>
                  </div>
                  <p v-if="errors.password" class="mt-1 text-sm text-red-500">
                    {{ errors.password }}
                  </p>
                </div>

                <div class="flex items-center">
                  <input
                    v-model="remember_me"
                    type="checkbox"
                    id="remember"
                    class="w-4 h-4 text-blue-500 border-sky-900/50 rounded focus:ring-blue-500 focus:ring-offset-gray-800"
                  />
                  <label for="remember" class="ml-2 text-sm text-blue-200"> Lembrar-me </label>
                </div>

                <button
                  type="submit"
                  class="w-full bg-[#60a5fa] hover:bg-[#4299e1] text-white py-3 px-4 rounded-lg font-medium transition-colors duration-300 flex items-center justify-center"
                  :disabled="isLoading('login')"
                >
                  <Icon v-if="isLoading('login')" icon="svg-spinners:ring-resize" class="w-6 h-6" />
                  <div v-else class="flex items-center gap-2">
                    <span>Entrar</span>
                    <Icon icon="lucide:log-in" />
                  </div>
                </button>

                <div class="relative flex items-center py-2">
                  <div class="flex-grow border-t border-sky-900/30"></div>
                </div>
              </form>

              <div class="mt-8 text-center">
                <p class="text-blue-200 text-sm">
                  Ainda não tem uma conta?
                  <RouterLink
                    to="/auth/register"
                    class="text-[#60a5fa] hover:text-[#4299e1] font-medium ml-1 transition-colors"
                  >
                    Crie uma agora
                  </RouterLink>
                </p>
              </div>
            </div>
          </div>

          <div class="mt-8 text-center">
            <p class="text-blue-200 text-xs">
              © GLOBAL Pagamentos © {{ new Date().getFullYear() }} - Todos os direitos reservados
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useAuthService } from '@/composables/auth/authService'
import { useRouter } from 'vue-router'
import { useLoaderStore } from '@/stores/loader'
import { Icon } from '@iconify/vue'
import { useGlobalToastStore } from '@/stores/global-toast'
import * as yup from 'yup'

const { login } = useAuthService()
const router = useRouter()
const { startLoading, stopLoading, isLoading } = useLoaderStore()
const { successToast, errorToast } = useGlobalToastStore()

const email = ref('')
const password = ref('')
const remember_me = ref(false)
const show_password = ref(false)
const hasAttemptedSubmit = ref(false)

const errors = reactive({
  email: '',
  password: '',
})

const validationSchema = yup.object().shape({
  email: yup.string().email('Por favor, insira um email válido').required('O email é obrigatório'),
  password: yup
    .string()
    .min(6, 'A senha deve ter pelo menos 6 caracteres')
    .required('A senha é obrigatória'),
})

const validateField = async (field: 'email' | 'password', value: string) => {
  if (!hasAttemptedSubmit.value && !value) {
    errors[field] = ''
    return
  }

  try {
    await validationSchema.validateAt(field, { [field]: value })
    errors[field] = ''
  } catch (err) {
    if (err instanceof yup.ValidationError) {
      if (!hasAttemptedSubmit.value && err.message.includes('obrigatório')) {
        errors[field] = ''
      } else {
        errors[field] = err.message
      }
    }
  }
}

const clearFieldError = (field: 'email' | 'password') => {
  errors[field] = ''
}

const toggle_password = () => {
  show_password.value = !show_password.value
}

const handle_login = async () => {
  hasAttemptedSubmit.value = true

  try {
    await validationSchema.validate(
      { email: email.value, password: password.value },
      { abortEarly: false },
    )

    startLoading('login')
    await login({
      email: email.value,
      password: password.value,
      remember_me: remember_me.value,
    })
    router.push('/dashboard')
    successToast('Login realizado com sucesso!')
  } catch (error) {
    if (error instanceof yup.ValidationError) {
      error.inner.forEach((err) => {
        if (err.path) {
          errors[err.path as 'email' | 'password'] = err.message
        }
      })
    } else {
      errorToast(
        error.response?.data?.message,
        'Email ou senha inválidos. Por favor, tente novamente.',
      )
    }
  } finally {
    stopLoading('login')
  }
}
</script>

<style scoped>
@keyframes formFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-form {
  animation: formFadeIn 0.6s ease-out forwards;
}

input:hover {
  border-color: rgba(96, 165, 250, 0.5);
}

input.border-red-500:hover {
  border-color: rgb(239, 68, 68);
}

@media (max-width: 640px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}
</style>
