<template>
  <div class="min-h-screen overflow-x-hidden bg-[#111827] flex flex-col">
    <div
      class="relative overflow-hidden flex-grow flex items-center justify-center min-h-[calc(100vh-80px)]"
    >
      <div class="absolute w-full h-full">
        <div class="absolute inset-0 bg-[#111827] opacity-70"></div>
        <img
          src="@/assets/auth/globo-auth.jpg"
          alt="Background"
          class="absolute inset-0 w-full h-full object-cover opacity-30"
        />
      </div>

      <div class="container mx-auto px-4 md:px-8 relative z-10 py-12">
        <div class="max-w-xl mx-auto">
          <div
            class="bg-[#1e2c4a]/80 backdrop-blur-sm rounded-xl shadow-lg overflow-hidden border border-sky-900/30"
          >
            <div class="p-6 sm:p-8 border-b border-sky-900/30">
              <h2 class="text-2xl sm:text-3xl font-bold text-white mb-2">Crie sua conta</h2>
              <p class="text-blue-200 text-sm sm:text-base">
                Junte-se à Global e comece a gerenciar seus pagamentos
              </p>
            </div>

            <div class="p-6 sm:p-8">
              <!-- Tabs Navigation -->
              <div class="flex mb-6 border-b border-sky-900/30">
                <button
                  @click="active_tab = 'pf'"
                  type="button"
                  :class="[
                    'w-1/2 py-2 px-4 text-sm font-medium flex items-center justify-center rounded-t-lg transition-colors focus:outline-none',
                    active_tab === 'pf'
                      ? 'bg-[#1e2c4a] text-[#60a5fa] border-b-2 border-[#60a5fa]'
                      : 'text-blue-200 hover:text-[#60a5fa]',
                  ]"
                >
                  <Icon icon="mdi:user" class="w-5 h-5 mr-2" />
                  <span>Pessoa Física</span>
                </button>
                <button
                  @click="active_tab = 'pj'"
                  type="button"
                  :class="[
                    'w-1/2 py-2 px-4 text-sm font-medium flex items-center justify-center rounded-t-lg transition-colors focus:outline-none',
                    active_tab === 'pj'
                      ? 'bg-[#1e2c4a] text-[#60a5fa] border-b-2 border-[#60a5fa]'
                      : 'text-blue-200 hover:text-[#60a5fa]',
                  ]"
                >
                  <Icon icon="mdi:building" class="w-5 h-5 mr-2" />
                  <span>Pessoa Jurídica</span>
                </button>
              </div>

              <form @submit.prevent="handle_register" class="space-y-5 animate-form">
                <!-- Pessoa Física Fields -->
                <template v-if="active_tab === 'pf'">
                  <div>
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-5">
                      <div>
                        <label class="block text-white text-sm font-medium mb-2" for="firstName">
                          Nome
                        </label>
                        <div class="relative">
                          <div
                            class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                          >
                            <Icon icon="mdi:user" class="w-5 h-5 text-blue-400" />
                          </div>
                          <input
                            v-model="first_name"
                            @blur="validateField('first_name', first_name)"
                            @input="clearFieldError('first_name')"
                            class="w-full bg-[#111827]/80 text-white border rounded-lg py-3 pl-10 pr-4 focus:outline-none focus:border-[#60a5fa] focus:ring-1 focus:ring-[#60a5fa] transition-colors"
                            :class="{
                              'border-red-500': errors.first_name,
                              'border-sky-900/50': !errors.first_name,
                            }"
                            type="text"
                            id="firstName"
                            placeholder="Seu nome"
                          />
                        </div>
                        <p v-if="errors.first_name" class="mt-1 text-sm text-red-500">
                          {{ errors.first_name }}
                        </p>
                      </div>

                      <div>
                        <label class="block text-white text-sm font-medium mb-2" for="lastName">
                          Sobrenome
                        </label>
                        <div class="relative">
                          <div
                            class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                          >
                            <Icon icon="mdi:user" class="w-5 h-5 text-blue-400" />
                          </div>
                          <input
                            v-model="last_name"
                            @blur="validateField('last_name', last_name)"
                            @input="clearFieldError('last_name')"
                            class="w-full bg-[#111827]/80 text-white border rounded-lg py-3 pl-10 pr-4 focus:outline-none focus:border-[#60a5fa] focus:ring-1 focus:ring-[#60a5fa] transition-colors"
                            :class="{
                              'border-red-500': errors.last_name,
                              'border-sky-900/50': !errors.last_name,
                            }"
                            type="text"
                            id="lastName"
                            placeholder="Seu sobrenome"
                          />
                        </div>
                        <p v-if="errors.last_name" class="mt-1 text-sm text-red-500">
                          {{ errors.last_name }}
                        </p>
                      </div>
                    </div>

                    <div>
                      <label class="block text-white text-sm font-medium mb-2 mt-4" for="document">
                        CPF
                      </label>
                      <div class="relative">
                        <div
                          class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                        >
                          <Icon icon="mdi:id-card" class="w-5 h-5 text-blue-400" />
                        </div>
                        <input
                          v-model="document"
                          v-mask="cpf_mask"
                          @blur="validateField('document', document)"
                          @input="clearFieldError('document')"
                          class="w-full bg-[#111827]/80 text-white border rounded-lg py-3 pl-10 pr-4 focus:outline-none focus:border-[#60a5fa] focus:ring-1 focus:ring-[#60a5fa] transition-colors"
                          :class="{
                            'border-red-500': errors.document,
                            'border-sky-900/50': !errors.document,
                          }"
                          type="text"
                          id="document"
                          placeholder="000.000.000-00"
                        />
                      </div>
                      <p v-if="errors.document" class="mt-1 text-sm text-red-500">
                        {{ errors.document }}
                      </p>
                    </div>
                  </div>
                </template>

                <!-- Pessoa Jurídica Fields -->
                <template v-if="active_tab === 'pj'">
                  <div>
                    <div>
                      <label class="block text-white text-sm font-medium mb-2" for="companyName">
                        Nome da Empresa
                      </label>
                      <div class="relative">
                        <div
                          class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                        >
                          <Icon icon="mdi:building" class="w-5 h-5 text-blue-400" />
                        </div>
                        <input
                          v-model="company_name"
                          @blur="validateField('company_name', company_name)"
                          @input="clearFieldError('company_name')"
                          class="w-full bg-[#111827]/80 text-white border rounded-lg py-3 pl-10 pr-4 focus:outline-none focus:border-[#60a5fa] focus:ring-1 focus:ring-[#60a5fa] transition-colors"
                          :class="{
                            'border-red-500': errors.company_name,
                            'border-sky-900/50': !errors.company_name,
                          }"
                          type="text"
                          id="companyName"
                          placeholder="Nome da sua empresa"
                        />
                      </div>
                      <p v-if="errors.company_name" class="mt-1 text-sm text-red-500">
                        {{ errors.company_name }}
                      </p>
                    </div>

                    <div>
                      <label class="block text-white text-sm font-medium mb-2 mt-4" for="document">
                        CNPJ
                      </label>
                      <div class="relative">
                        <div
                          class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                        >
                          <Icon icon="mdi:id-card" class="w-5 h-5 text-blue-400" />
                        </div>
                        <input
                          v-model="document"
                          v-mask="cnpj_mask"
                          @blur="validateField('document', document)"
                          @input="clearFieldError('document')"
                          class="w-full bg-[#111827]/80 text-white border rounded-lg py-3 pl-10 pr-4 focus:outline-none focus:border-[#60a5fa] focus:ring-1 focus:ring-[#60a5fa] transition-colors"
                          :class="{
                            'border-red-500': errors.document,
                            'border-sky-900/50': !errors.document,
                          }"
                          type="text"
                          id="document"
                          placeholder="00.000.000/0000-00"
                        />
                      </div>
                      <p v-if="errors.document" class="mt-1 text-sm text-red-500">
                        {{ errors.document }}
                      </p>
                    </div>

                    <div>
                      <div>
                        <label
                          class="block text-white text-sm font-medium mb-2 mt-4"
                          for="responsibleName"
                        >
                          Nome do Responsável
                        </label>
                        <div class="relative">
                          <div
                            class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                          >
                            <Icon icon="mdi:user" class="w-5 h-5 text-blue-400" />
                          </div>
                          <input
                            v-model="responsible_name"
                            @blur="validateField('responsible_name', responsible_name)"
                            @input="clearFieldError('responsible_name')"
                            class="w-full bg-[#111827]/80 text-white border rounded-lg py-3 pl-10 pr-4 focus:outline-none focus:border-[#60a5fa] focus:ring-1 focus:ring-[#60a5fa] transition-colors"
                            :class="{
                              'border-red-500': errors.responsible_name,
                              'border-sky-900/50': !errors.responsible_name,
                            }"
                            type="text"
                            id="responsibleName"
                            placeholder="Nome completo"
                          />
                        </div>
                        <p v-if="errors.responsible_name" class="mt-1 text-sm text-red-500">
                          {{ errors.responsible_name }}
                        </p>
                      </div>
                    </div>
                  </div>
                </template>

                <div>
                  <label class="block text-white text-sm font-medium mb-2" for="email">
                    Email
                  </label>
                  <div class="relative">
                    <div
                      class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                    >
                      <Icon icon="mdi:email" class="w-5 h-5 text-blue-400" />
                    </div>
                    <input
                      v-model="email"
                      @blur="validateField('email', email)"
                      @input="clearFieldError('email')"
                      class="w-full bg-[#111827]/80 text-white border rounded-lg py-3 pl-10 pr-4 focus:outline-none focus:border-[#60a5fa] focus:ring-1 focus:ring-[#60a5fa] transition-colors"
                      :class="{
                        'border-red-500': errors.email,
                        'border-sky-900/50': !errors.email,
                      }"
                      type="email"
                      id="email"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <p v-if="errors.email" class="mt-1 text-sm text-red-500">{{ errors.email }}</p>
                </div>

                <div>
                  <label class="block text-white text-sm font-medium mb-2" for="phone">
                    Telefone
                  </label>
                  <div class="relative">
                    <div
                      class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                    >
                      <Icon icon="mdi:phone" class="w-5 h-5 text-blue-400" />
                    </div>
                    <input
                      v-model="phone"
                      v-mask="phone_mask"
                      @blur="validateField('phone', phone)"
                      @input="clearFieldError('phone')"
                      class="w-full bg-[#111827]/80 text-white border rounded-lg py-3 pl-10 pr-4 focus:outline-none focus:border-[#60a5fa] focus:ring-1 focus:ring-[#60a5fa] transition-colors"
                      :class="{
                        'border-red-500': errors.phone,
                        'border-sky-900/50': !errors.phone,
                      }"
                      type="tel"
                      id="phone"
                      placeholder="(00) 00000-0000"
                    />
                  </div>
                  <p v-if="errors.phone" class="mt-1 text-sm text-red-500">{{ errors.phone }}</p>
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 gap-5">
                  <div>
                    <label class="block text-white text-sm font-medium mb-2" for="password">
                      Senha
                    </label>
                    <div class="relative">
                      <div
                        class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                      >
                        <Icon icon="mdi:lock" class="w-5 h-5 text-blue-400" />
                      </div>
                      <input
                        v-model="password"
                        @blur="validateField('password', password)"
                        @input="clearFieldError('password')"
                        class="w-full bg-[#111827]/80 text-white border rounded-lg py-3 pl-10 pr-4 focus:outline-none focus:border-[#60a5fa] focus:ring-1 focus:ring-[#60a5fa] transition-colors"
                        :class="{
                          'border-red-500': errors.password,
                          'border-sky-900/50': !errors.password,
                        }"
                        :type="show_password ? 'text' : 'password'"
                        id="password"
                        placeholder="••••••••"
                      />
                      <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <button
                          type="button"
                          @click="toggle_password"
                          class="text-blue-400 hover:text-blue-300 focus:outline-none"
                        >
                          <Icon
                            :icon="show_password ? 'mdi:eye-off' : 'mdi:eye'"
                            class="w-5 h-5 text-blue-400"
                          />
                        </button>
                      </div>
                    </div>
                    <p v-if="errors.password" class="mt-1 text-sm text-red-500">
                      {{ errors.password }}
                    </p>
                  </div>

                  <div>
                    <label class="block text-white text-sm font-medium mb-2" for="passwordConfirm">
                      Confirmar Senha
                    </label>
                    <div class="relative">
                      <div
                        class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                      >
                        <Icon icon="mdi:lock" class="w-5 h-5 text-blue-400" />
                      </div>
                      <input
                        v-model="password_confirm"
                        @blur="validateField('password_confirm', password_confirm)"
                        @input="clearFieldError('password_confirm')"
                        class="w-full bg-[#111827]/80 text-white border rounded-lg py-3 pl-10 pr-4 focus:outline-none focus:border-[#60a5fa] focus:ring-1 focus:ring-[#60a5fa] transition-colors"
                        :class="{
                          'border-red-500': errors.password_confirm,
                          'border-sky-900/50': !errors.password_confirm,
                        }"
                        :type="show_password ? 'text' : 'password'"
                        id="passwordConfirm"
                        placeholder="••••••••"
                      />
                    </div>
                    <p v-if="errors.password_confirm" class="mt-1 text-sm text-red-500">
                      {{ errors.password_confirm }}
                    </p>
                  </div>
                </div>

                <div class="flex items-start">
                  <div class="flex items-center h-5">
                    <input
                      v-model="agree_terms"
                      @change="validateField('agree_terms', agree_terms)"
                      id="terms"
                      type="checkbox"
                      class="w-4 h-4 text-blue-500 border-sky-900/50 rounded focus:ring-blue-500 focus:ring-offset-gray-800"
                      :class="{
                        'border-red-500': errors.agree_terms,
                        'border-sky-900/50': !errors.agree_terms,
                      }"
                    />
                  </div>
                  <div class="ml-3 text-sm">
                    <label for="terms" class="text-blue-200">
                      Eu concordo com os
                      <a href="#" class="text-[#60a5fa] hover:text-[#4299e1] font-medium"
                        >Termos de Serviço</a
                      >
                      e
                      <a href="#" class="text-[#60a5fa] hover:text-[#4299e1] font-medium"
                        >Política de Privacidade</a
                      >
                    </label>
                    <p v-if="errors.agree_terms" class="mt-1 text-sm text-red-500">
                      {{ errors.agree_terms }}
                    </p>
                  </div>
                </div>

                <button
                  type="submit"
                  class="w-full bg-[#60a5fa] hover:bg-[#4299e1] text-white py-3 px-4 rounded-lg font-medium transition-colors duration-300 flex items-center justify-center"
                >
                  <span>Criar conta</span>
                  <Icon icon="mdi:user-plus" class="w-5 h-5 ml-2" />
                </button>

                <div class="relative flex items-center py-2">
                  <div class="flex-grow border-t border-sky-900/30"></div>
                </div>
              </form>

              <div class="mt-8 text-center">
                <p class="text-blue-200 text-sm">
                  Já tem uma conta?
                  <RouterLink
                    to="/auth/login"
                    class="text-[#60a5fa] hover:text-[#4299e1] font-medium ml-1 transition-colors"
                  >
                    Faça login
                  </RouterLink>
                </p>
              </div>
            </div>
          </div>
          <div class="mt-8 text-center">
            <p class="text-blue-200 text-xs">
              © GLOBAL Pagamentos © {{ new Date().getFullYear() }} - Todos os direitos reservados
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useAuthService } from '@/composables/auth/authService'
import { useRouter } from 'vue-router'
import { useGlobalToastStore } from '@/stores/global-toast'
import * as yup from 'yup'
import { Icon } from '@iconify/vue'

const { register } = useAuthService()
const router = useRouter()
const { successToast, errorToast } = useGlobalToastStore()

// Tab control
const active_tab = ref('pf')
const hasAttemptedSubmit = ref(false)

// Common fields
const email = ref('')
const phone = ref('')
const password = ref('')
const password_confirm = ref('')
const agree_terms = ref(false)
const show_password = ref(false)
const document = ref('')

// Pessoa Física fields
const first_name = ref('')
const last_name = ref('')

// Pessoa Jurídica fields
const company_name = ref('')
const responsible_name = ref('')

// Máscaras
const cpf_mask = '###.###.###-##'
const cnpj_mask = '##.###.###/####-##'
const phone_mask = '(##) #####-####'

// Estados de erro
const errors = reactive({
  email: '',
  phone: '',
  password: '',
  password_confirm: '',
  agree_terms: '',
  document: '',
  first_name: '',
  last_name: '',
  company_name: '',
  responsible_name: '',
})

// Schema base para campos comuns
const baseSchema = {
  email: yup.string().email('Por favor, insira um email válido').required('O email é obrigatório'),
  phone: yup
    .string()
    .matches(/^\(\d{2}\) \d{5}-\d{4}$/, 'Telefone inválido')
    .required('O telefone é obrigatório'),
  password: yup
    .string()
    .min(6, 'A senha deve ter pelo menos 6 caracteres')
    .required('A senha é obrigatória'),
  password_confirm: yup
    .string()
    .oneOf([yup.ref('password')], 'As senhas não coincidem')
    .required('A confirmação de senha é obrigatória'),
  agree_terms: yup.boolean().oneOf([true], 'Você precisa concordar com os termos'),
}

// Schema para Pessoa Física
const pfSchema = yup.object().shape({
  ...baseSchema,
  first_name: yup
    .string()
    .min(2, 'O nome deve ter pelo menos 2 caracteres')
    .required('O nome é obrigatório'),
  last_name: yup
    .string()
    .min(2, 'O sobrenome deve ter pelo menos 2 caracteres')
    .required('O sobrenome é obrigatório'),
  document: yup
    .string()
    .matches(/^\d{3}\.\d{3}\.\d{3}-\d{2}$/, 'CPF inválido')
    .required('O CPF é obrigatório'),
})

// Schema para Pessoa Jurídica
const pjSchema = yup.object().shape({
  ...baseSchema,
  company_name: yup
    .string()
    .min(2, 'O nome da empresa deve ter pelo menos 2 caracteres')
    .required('O nome da empresa é obrigatório'),
  responsible_name: yup
    .string()
    .min(2, 'O nome do responsável deve ter pelo menos 2 caracteres')
    .required('O nome do responsável é obrigatório'),
  document: yup
    .string()
    .matches(/^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/, 'CNPJ inválido')
    .required('O CNPJ é obrigatório'),
})

// Schema ativo baseado na tab selecionada
const activeSchema = computed(() => (active_tab.value === 'pf' ? pfSchema : pjSchema))

const validateField = async (field: string, value: string | boolean) => {
  // Só valida se já tentou submeter ou se o campo tem algum valor
  if (!hasAttemptedSubmit.value && !value) {
    errors[field] = ''
    return
  }

  try {
    await activeSchema.value.validateAt(field, { [field]: value })
    errors[field] = ''
  } catch (err) {
    if (err instanceof yup.ValidationError) {
      // Se não tentou submeter ainda, só mostra erro de formato, não de campo obrigatório
      if (!hasAttemptedSubmit.value && err.message.includes('obrigatório')) {
        errors[field] = ''
      } else {
        errors[field] = err.message
      }
    }
  }
}

const clearFieldError = (field: string) => {
  errors[field] = ''
}

const toggle_password = () => {
  show_password.value = !show_password.value
}

const handle_register = async () => {
  hasAttemptedSubmit.value = true

  try {
    const formData = {
      email: email.value,
      phone: phone.value,
      password: password.value,
      password_confirmation: password_confirm.value,
      agreement_terms: agree_terms.value,
      document: document.value,
      document_type: active_tab.value === 'pf' ? 'cpf' : 'cnpj',
      ...(active_tab.value === 'pf'
        ? {
            first_name: first_name.value,
            last_name: last_name.value,
          }
        : {
            company_name: company_name.value,
            responsible_name: responsible_name.value,
          }),
    }

    await activeSchema.value.validate(formData, { abortEarly: false })
    await register(formData)
    successToast('Conta criada com sucesso!')
    router.push('/auth/login')
  } catch (error) {
    if (error instanceof yup.ValidationError) {
      error.inner.forEach((err) => {
        if (err.path) {
          errors[err.path] = err.message
        }
      })
    } else {
      errorToast(
        'Erro ao criar conta',
        'Ocorreu um erro ao criar sua conta. Por favor, tente novamente.',
      )
    }
  }
}
</script>

<style scoped>
@keyframes formFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-form {
  animation: formFadeIn 0.6s ease-out forwards;
}

/* Efeito hover nos inputs */
input:hover {
  border-color: rgba(96, 165, 250, 0.5);
}

/* Media queries */
@media (max-width: 640px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

input.border-red-500:hover {
  border-color: rgb(239, 68, 68);
}
</style>
