<?php

namespace Database\Factories;

use App\Models\PixAccount;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PixAccount>
 */
class PixAccountFactory extends Factory
{
    protected $model = PixAccount::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'bank_code' => '748', // Sicredi
            'account_number' => $this->faker->numerify('########'),
            'account_type' => $this->faker->randomElement(['corrente', 'poupanca']),
            'is_active' => true,
            'balance' => $this->faker->numberBetween(0, 100000), // 0 a R$ 1000.00 em centavos
            'pix_key' => $this->faker->randomElement([
                $this->faker->email,
                $this->faker->numerify('###########'), // CPF
                $this->faker->numerify('+55##9########'), // Telefone
                $this->faker->uuid
            ]),
            'pix_key_type' => $this->faker->randomElement(['email', 'cpf', 'phone', 'random']),
        ];
    }

    /**
     * Indicate that the account is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the account has no balance.
     */
    public function noBalance(): static
    {
        return $this->state(fn (array $attributes) => [
            'balance' => 0,
        ]);
    }

    /**
     * Set a specific balance.
     */
    public function withBalance(int $balanceInCents): static
    {
        return $this->state(fn (array $attributes) => [
            'balance' => $balanceInCents,
        ]);
    }
}
