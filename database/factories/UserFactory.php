<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'first_name' => fake()->firstName(),
            'last_name' => fake()->lastName(),
            'email' => fake()->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => static::$password ??= Hash::make('password'),
            'phone' => fake()->phoneNumber(),
            'document' => fake()->numerify('###########'), // CPF
            'document_type' => 'cpf',
            'status' => 'active',
            'agreement_terms' => true,
            'kyc_status' => fake()->randomElement(['pending', 'under_review', 'verified']),
            'webhook_url' => fake()->optional()->url(),
            'remember_token' => Str::random(10),
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }

    /**
     * Indicate that the user is a company (PJ).
     */
    public function company(): static
    {
        return $this->state(fn (array $attributes) => [
            'document' => fake()->numerify('##############'), // CNPJ
            'document_type' => 'cnpj',
            'document_company' => fake()->numerify('###########'), // CPF do representante
            'company_legal_name' => fake()->company(),
            'company_trading_name' => fake()->company(),
        ]);
    }

    /**
     * Indicate that the user has KYC verified.
     */
    public function kycVerified(): static
    {
        return $this->state(fn (array $attributes) => [
            'kyc_status' => 'verified',
            'kyc_verified_at' => now(),
        ]);
    }

    /**
     * Indicate that the user has KYC rejected.
     */
    public function kycRejected(): static
    {
        return $this->state(fn (array $attributes) => [
            'kyc_status' => 'rejected',
            'kyc_rejection_reason' => 'Documentos inválidos',
        ]);
    }
}
