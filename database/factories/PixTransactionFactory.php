<?php

namespace Database\Factories;

use App\Models\PixTransaction;
use App\Models\User;
use App\Models\PixAccount;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PixTransaction>
 */
class PixTransactionFactory extends Factory
{
    protected $model = PixTransaction::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $userId = User::factory();

        return [
            'user_id' => $userId,
            'pix_account_id' => PixAccount::factory()->state(['user_id' => $userId]),
            'pix_transaction_id' => Str::uuid(),
            'order_id' => Str::uuid() . '-' . $this->faker->randomNumber(),
            'transaction_id_external' => Str::uuid() . '-' . $this->faker->randomNumber(),
            'type' => $this->faker->randomElement(['deposit', 'withdraw']),
            'amount' => $this->faker->numberBetween(100, 100000), // R$ 1.00 a R$ 1000.00 em centavos
            'status' => $this->faker->randomElement(['pending', 'pending_payment', 'received', 'completed', 'failed']),
            'description' => $this->faker->sentence(),
            'pix_key' => $this->faker->randomElement([
                $this->faker->email,
                $this->faker->numerify('###########'), // CPF
                $this->faker->numerify('+55##9########'), // Telefone
            ]),
            'qr_code' => null,
            'document_beneficiary' => $this->faker->numerify('###########'),
            'external_transaction_id' => $this->faker->optional()->uuid,
            'processed_at' => $this->faker->optional()->dateTimeThisYear(),
        ];
    }

    /**
     * Indicate that the transaction is a deposit.
     */
    public function deposit(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'deposit',
            'qr_code' => '00020101021226800014br.gov.bcb.pix...',
        ]);
    }

    /**
     * Indicate that the transaction is a withdraw.
     */
    public function withdraw(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'withdraw',
            'qr_code' => null,
        ]);
    }

    /**
     * Indicate that the transaction is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'processed_at' => null,
        ]);
    }

    /**
     * Indicate that the transaction is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'processed_at' => now(),
        ]);
    }

    /**
     * Indicate that the transaction has failed.
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'failed',
            'processed_at' => now(),
        ]);
    }

    /**
     * Set a specific amount.
     */
    public function withAmount(int $amountInCents): static
    {
        return $this->state(fn (array $attributes) => [
            'amount' => $amountInCents,
        ]);
    }
}
