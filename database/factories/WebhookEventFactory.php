<?php

namespace Database\Factories;

use App\Models\PixTransaction;
use App\Models\WebhookEvent;
use Illuminate\Database\Eloquent\Factories\Factory;

class WebhookEventFactory extends Factory
{
    protected $model = WebhookEvent::class;

    public function definition(): array
    {
        return [
            'pix_transaction_id' => PixTransaction::factory(),
            'event_type' => $this->faker->randomElement([
                'transaction.created',
                'transaction.completed',
                'transaction.failed',
                'deposit.received',
                'withdraw.processed'
            ]),
            'payload' => [
                'transaction_id' => $this->faker->uuid,
                'amount' => $this->faker->randomFloat(2, 1, 1000),
                'status' => $this->faker->randomElement(['pending', 'completed', 'failed']),
                'created_at' => now()->toISOString()
            ],
            'status' => 'pending',
            'attempts' => 0,
            'last_attempt_at' => null,
            'processed_at' => null,
            'failed_at' => null,
            'error_message' => null,
        ];
    }

    public function processed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'processed',
            'attempts' => 1,
            'last_attempt_at' => now(),
            'processed_at' => now(),
        ]);
    }

    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'failed',
            'attempts' => 3,
            'last_attempt_at' => now(),
            'failed_at' => now(),
            'error_message' => 'Connection timeout',
        ]);
    }

    public function retrying(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'attempts' => 2,
            'last_attempt_at' => now()->subMinutes(10),
        ]);
    }
}
