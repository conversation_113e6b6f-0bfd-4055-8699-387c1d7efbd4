<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('first_name');
            $table->string('last_name');
            $table->string('email')->unique()->index();
            $table->timestamp('email_verified_at')->nullable();
            $table->enum('role', ['user', 'customer', 'admin'])->default('user');
            $table->enum('status', ['active', 'inactive'])->default('active')->index();
            $table->string('password');
            $table->string('phone')->unique();
            $table->boolean('agreement_terms')->default(false);
            $table->string('webhook_url')->nullable();
            $table->enum('document_type', ['cpf', 'cnpj'])->default('cpf');
            $table->string('document')->unique()->index();
            $table->string('document_company')->unique()->nullable()->index();
            $table->string('company_legal_name')->nullable();
            $table->string('company_trading_name')->nullable();
            $table->string('company_contract_url')->nullable();
            $table->string('company_statute_url')->nullable();
            $table->string('company_proxy_url')->nullable();
            $table->enum('kyc_status', ['pending', 'under_review', 'verified', 'rejected'])->default('pending');
            $table->string('document_front_url')->nullable();
            $table->string('document_back_url')->nullable();
            $table->string('selfie_url')->nullable();
            $table->timestamp('kyc_verified_at')->nullable();
            $table->text('kyc_rejection_reason')->nullable();
            $table->rememberToken();
            $table->softDeletes();
            $table->timestamps();
        });

        Schema::create('password_reset_tokens', function (Blueprint $table) {
            $table->string('email')->primary();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
        });

        Schema::create('sessions', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->foreignId('user_id')->nullable()->index();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->longText('payload');
            $table->integer('last_activity')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('sessions');
    }
};
