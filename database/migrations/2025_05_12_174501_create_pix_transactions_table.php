<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pix_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users');
            $table->foreignId('pix_account_id')->constrained('pix_accounts');
            $table->uuid('pix_transaction_id');
            $table->string('order_id')->nullable();
            $table->string('transaction_id_external')->nullable();
            $table->enum('type', ['deposit', 'withdraw', 'refund', 'fee', 'transfer']);
            $table->integer('amount')->default(0);
            $table->enum('status', ['pending', 'pending_payment', 'received', 'completed', 'failed', 'cancelled'])->default('pending');
            $table->text('description')->nullable();
            $table->string('pix_key');
            $table->text('qr_code')->nullable();
            $table->string('document_beneficiary')->nullable();
            $table->string('external_transaction_id')->nullable(); // ID da transação no gateway externo
            $table->timestamp('processed_at')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'status']);
            $table->index(['type', 'status']);
            $table->index('transaction_id_external');
            $table->index('external_transaction_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pix_transactions');
    }
};
