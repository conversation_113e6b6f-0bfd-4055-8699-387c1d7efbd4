{"name": "global", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@iconify/vue": "^5.0.0", "@tailwindcss/vite": "^4.1.5", "@vueuse/core": "^13.1.0", "apexcharts": "^4.7.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "lodash": "^4.17.21", "lucide-vue-next": "^0.503.0", "pinia": "^2.2.6", "reka-ui": "^2.2.0", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.5", "tw-animate-css": "^1.2.8", "vue": "^3.5.13", "vue-router": "^4.4.5", "vue-the-mask": "^0.11.1", "vue3-apexcharts": "^1.8.0", "yup": "^1.6.1"}, "devDependencies": {"@tsconfig/node22": "^22.0.0", "@types/axios": "^0.14.4", "@types/lodash": "^4.17.16", "@types/node": "^22.15.17", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.1.3", "@vue/tsconfig": "^0.7.0", "eslint": "^9.14.0", "eslint-plugin-vue": "^9.30.0", "npm-run-all2": "^7.0.1", "prettier": "^3.3.3", "typescript": "~5.6.3", "vite": "^6.0.1", "vite-plugin-vue-devtools": "^7.6.5", "vue-tsc": "^2.1.10"}}