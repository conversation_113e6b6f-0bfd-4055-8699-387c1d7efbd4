# Arquitetura e Fluxo de Rotas - API Laravel PIX

## Diagrama ER das Entidades Principais

```mermaid
erDiagram
    User {
        uuid id
        string name
        string email
        string password
    }
    ApiKey {
        uuid id
        string key
        string secret
        string status
        uuid user_id
    }
    PixAccount {
        uuid id
        string bank_code
        string account_number
        string account_type
        string pix_key
        string pix_key_type
        string status
        uuid user_id
    }
    PixTransaction {
        uuid id
        float amount
        string description
        string status
        uuid pix_account_id
    }
    WebhookEvent {
        uuid id
        string event_type
        string payload
        uuid pix_transaction_id
    }

    User ||--o{ ApiKey : "possui"
    User ||--o{ PixAccount : "possui"
    PixAccount ||--o{ PixTransaction : "possui"
    PixTransaction ||--o{ WebhookEvent : "gera"
```

---

## Diagrama de Arquitetura Geral

```mermaid
graph TB
    subgraph "Cliente/Integração"
        A[Cliente/Frontend]
        B[Sistema Externo]
    end
    
    subgraph "Gateway/Proxy"
        C[Nginx/Apache]
    end
    
    subgraph "Aplicação Laravel"
        D[Middleware]
        E[Rotas]
        F[Controllers]
        G[Services]
        H[Models]
        I[Database]
    end
    
    subgraph "Serviços Externos"
        J[Serviço PIX]
        K[Serviço KYC]
        L[Serviço de Notificações]
    end
    
    A --> C
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I
    G --> J
    G --> K
    G --> L
```

## Fluxo Detalhado das Rotas

### 1. Rotas de Autenticação

```mermaid
sequenceDiagram
    participant C as Cliente
    participant R as Router
    participant M as Middleware
    participant AC as AuthController
    participant AS as AuthService
    participant DB as Database
    
    Note over C,DB: POST /api/v1/auth/register
    C->>R: Request com dados + documentos
    R->>AC: register()
    AC->>AS: register(data)
    AS->>AS: Validar dados
    AS->>AS: Upload documentos KYC
    AS->>DB: Criar User
    AS->>DB: Criar ApiKey
    AS->>C: Response com API Key/Secret
    
    Note over C,DB: POST /api/v1/auth/login
    C->>R: Request com email/password
    R->>AC: login()
    AC->>AS: login(credentials)
    AS->>DB: Validar credenciais
    AS->>AS: Gerar Sanctum Token
    AS->>C: Response com access_token
    
    Note over C,DB: POST /api/v1/auth/logout (auth:sanctum)
    C->>R: Request com Bearer Token
    R->>M: auth:sanctum
    M->>AC: logout()
    AC->>AS: logout()
    AS->>DB: Revogar token
    AS->>C: Response de sucesso
    
    Note over C,DB: GET /api/v1/me (auth:sanctum)
    C->>R: Request com Bearer Token
    R->>M: auth:sanctum
    M->>AC: me()
    AC->>AS: me()
    AS->>DB: Buscar usuário
    AS->>C: Response com dados do usuário
```

### 2. Rotas de Contas PIX (Protegidas por API Key)

```mermaid
sequenceDiagram
    participant C as Cliente
    participant R as Router
    participant M as Middleware
    participant PC as PixAccountController
    participant PS as PixAccountService
    participant DB as Database
    
    Note over C,DB: GET /api/v1/pix-accounts (api.key)
    C->>R: Request com api_key + api_secret
    R->>M: api.key
    M->>M: Validar API Key/Secret
    M->>PC: index()
    PC->>PS: list(user)
    PS->>DB: Buscar contas do usuário
    PS->>C: Response com lista de contas
    
    Note over C,DB: GET /api/v1/pix-accounts/{id} (api.key)
    C->>R: Request com ID da conta
    R->>M: api.key
    M->>PC: show(id)
    PC->>PS: findByPixAccountId(user, id)
    PS->>DB: Buscar conta específica
    PS->>C: Response com dados da conta
    
    Note over C,DB: GET /api/v1/pix-accounts/{id}/balance (api.key)
    C->>R: Request com ID da conta
    R->>M: api.key
    M->>PC: balance(id)
    PC->>PS: getBalance(user, id)
    PS->>DB: Calcular saldo
    PS->>C: Response com saldo
    
    Note over C,DB: PATCH /api/v1/pix-accounts/webhook (api.key)
    C->>R: Request com URL do webhook
    R->>M: api.key
    M->>PC: storeWebhook()
    PC->>PS: updateWebhookUrl()
    PS->>DB: Salvar URL do webhook
    PS->>C: Response de sucesso
```

### 3. Rotas de Transações PIX (Protegidas por API Key)

```mermaid
sequenceDiagram
    participant C as Cliente
    participant R as Router
    participant M as Middleware
    participant TC as PixTransactionController
    participant TS as PixTransactionService
    participant DB as Database
    participant PIX as Serviço PIX
    
    Note over C,PIX: POST /api/v1/pix-transactions/qrcode (api.key)
    C->>R: Request com dados do depósito
    R->>M: api.key
    M->>TC: deposit()
    TC->>TS: createDeposit(data)
    TS->>PIX: Gerar QR Code
    TS->>DB: Salvar transação
    TS->>C: Response com QR Code
    
    Note over C,PIX: POST /api/v1/pix-transactions/withdraw (api.key)
    C->>R: Request com dados do saque
    R->>M: api.key
    M->>TC: withdraw()
    TC->>TS: createWithdraw(data)
    TS->>PIX: Processar saque
    TS->>DB: Salvar transação
    TS->>C: Response com status
    
    Note over C,PIX: GET /api/v1/pix-transactions (api.key)
    C->>R: Request com filtros
    R->>M: api.key
    M->>TC: index()
    TC->>TS: list(user, filters)
    TS->>DB: Buscar transações
    TS->>C: Response com lista
    
    Note over C,PIX: GET /api/v1/pix-transactions/{id} (api.key)
    C->>R: Request com ID da transação
    R->>M: api.key
    M->>TC: show(id)
    TC->>TS: find(user, id)
    TS->>DB: Buscar transação
    TS->>C: Response com dados da transação
```

### 4. Webhook (Público)

```mermaid
sequenceDiagram
    participant PIX as Serviço PIX
    participant R as Router
    participant WC as WebhookController
    participant WS as WebhookService
    participant DB as Database
    
    Note over PIX,DB: POST /global/receipt/webhook
    PIX->>R: Notificação de transação
    R->>WC: webhook()
    WC->>WC: Validar payload
    WC->>WS: processWebhook(payload)
    WS->>DB: Atualizar status da transação
    WS->>DB: Registrar evento
    WS->>PIX: Response 200 OK
```

### 5. Rota de Health Check

```mermaid
sequenceDiagram
    participant C as Cliente
    participant R as Router
    
    Note over C,R: GET /api/v1/health
    C->>R: Request de health check
    R->>C: Response {"status": "ok"}
```

## Estrutura de Middleware

```mermaid
graph LR
    A[Request] --> B[Global Middleware]
    B --> C[Route Middleware]
    C --> D[Controller]
    
    subgraph "Global Middleware"
        B1[TrustProxies]
        B2[HandleCors]
        B3[PreventRequestsDuringMaintenance]
        B4[ValidatePostSize]
        B5[TrimStrings]
        B6[ConvertEmptyStringsToNull]
    end
    
    subgraph "Route Middleware"
        C1[auth:sanctum]
        C2[api.key]
        C3[throttle]
    end
    
    B --> B1
    B --> B2
    B --> B3
    B --> B4
    B --> B5
    B --> B6
    C --> C1
    C --> C2
    C --> C3
```

## Fluxo de Autenticação

```mermaid
stateDiagram-v2
    [*] --> Request
    Request --> APIKeyCheck
    Request --> SanctumCheck
    Request --> PublicRoute

    APIKeyCheck --> Controller : válido
    APIKeyCheck --> [*] : inválido

    SanctumCheck --> Controller : válido
    SanctumCheck --> [*] : inválido

    PublicRoute --> Controller

    Controller --> Response
    Response --> [*]
```

## Resumo das Rotas

| Método | Rota | Middleware | Controller | Descrição |
|--------|------|------------|------------|-----------|
| POST | `/api/v1/auth/register` | - | AuthController | Registrar usuário com KYC |
| POST | `/api/v1/auth/login` | - | AuthController | Login do usuário |
| POST | `/api/v1/auth/logout` | auth:sanctum | AuthController | Logout do usuário |
| GET | `/api/v1/me` | auth:sanctum | AuthController | Dados do usuário logado |
| GET | `/api/v1/pix-accounts` | api.key | PixAccountController | Listar contas PIX |
| GET | `/api/v1/pix-accounts/{id}` | api.key | PixAccountController | Buscar conta específica |
| GET | `/api/v1/pix-accounts/{id}/balance` | api.key | PixAccountController | Consultar saldo |
| PATCH | `/api/v1/pix-accounts/webhook` | api.key | PixAccountController | Configurar webhook |
| POST | `/api/v1/pix-transactions/qrcode` | api.key | PixTransactionController | Gerar QR Code para depósito |
| POST | `/api/v1/pix-transactions/withdraw` | api.key | PixTransactionController | Realizar saque |
| GET | `/api/v1/pix-transactions` | api.key | PixTransactionController | Listar transações |
| GET | `/api/v1/pix-transactions/{id}` | api.key | PixTransactionController | Buscar transação específica |
| POST | `/global/receipt/webhook` | - | WebhookController | Receber notificações PIX |
| GET | `/api/v1/health` | - | Closure | Health check da API |

## Componentes da Arquitetura

### Controllers
- **AuthController**: Gerencia autenticação e registro de usuários
- **PixAccountController**: Gerencia contas PIX dos usuários
- **PixTransactionController**: Gerencia transações PIX
- **WebhookController**: Processa notificações externas

### Services
- **AuthService**: Lógica de negócio para autenticação
- **PixAccountService**: Lógica de negócio para contas PIX
- **PixTransactionService**: Lógica de negócio para transações
- **KycService**: Processamento de KYC
- **WebhookService**: Processamento de webhooks

### Models
- **User**: Usuários da aplicação
- **ApiKey**: Chaves de API para autenticação
- **PixAccount**: Contas PIX dos usuários
- **PixTransaction**: Transações PIX
- **WebhookEvent**: Eventos de webhook

### Middleware
- **auth:sanctum**: Autenticação via Laravel Sanctum
- **api.key**: Autenticação via API Key/Secret customizada 
