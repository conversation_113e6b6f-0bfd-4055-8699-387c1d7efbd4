# Traits

## MonetaryConversion

Este trait fornece métodos utilitários para conversão de valores monetários entre reais e centavos.

### Como usar

```php
<?php

namespace App\Services;

use App\Traits\MonetaryConversion;

class MeuServico
{
    use MonetaryConversion;

    public function processarPagamento(float $valorEmReais)
    {
        // Converter reais para centavos
        $valorEmCentavos = $this->convertToCents($valorEmReais);
        
        // Processar pagamento...
        
        // Converter centavos de volta para reais
        $valorFinal = $this->convertToReais($valorEmCentavos);
        
        return $valorFinal;
    }
}
```

### Métodos disponíveis

- `convertToCents(float $amount): int` - Converte valor em reais para centavos
- `convertToReais(int $amountInCents): float` - Converte valor em centavos para reais

### Classes que já utilizam este trait

- `App\Services\PixTransactionService`
- `App\Services\PixAccountService` 
