<?php

namespace App\Traits;

trait MonetaryConversion
{
    /**
     * Converte um valor em reais para centavos
     *
     * @param float $amount Valor em reais
     * @return int Valor em centavos
     */
    public function convertToCents(float $amount): int
    {
        return (int) round($amount * 100);
    }

    /**
     * Converte um valor em centavos para reais
     *
     * @param int $amountInCents Valor em centavos
     * @return float Valor em reais
     */
    public function convertToReais(int $amountInCents): float
    {
        return $amountInCents / 100;
    }

    /**
     * Alias para convertToCents - para compatibilidade com testes
     */
    public function reaisToCents(float $amount): int
    {
        return $this->convertToCents($amount);
    }

    /**
     * Alias para convertToReais - para compatibilidade com testes
     */
    public function centsToReais(int $amountInCents): float
    {
        return $this->convertToReais($amountInCents);
    }

    /**
     * Formata um valor em reais para exibição
     *
     * @param float $amount Valor em reais
     * @return string Valor formatado (ex: R$ 100,00)
     */
    public function formatCurrency(float $amount): string
    {
        $formatted = number_format(abs($amount), 2, ',', '.');
        $prefix = $amount < 0 ? '-R$ ' : 'R$ ';
        return $prefix . $formatted;
    }

    /**
     * Valida se um valor monetário é válido
     *
     * @param mixed $amount
     * @return bool
     */
    public function isValidMonetaryAmount($amount): bool
    {
        return is_numeric($amount) && $amount >= 0;
    }

    /**
     * Converte string monetária para float
     * Ex: "R$ 1.234,56" -> 1234.56
     *
     * @param string $currencyString
     * @return float
     */
    public function parseCurrencyString(string $currencyString): float
    {
        // Remove símbolos e converte vírgula para ponto
        $cleaned = str_replace(['R$', ' ', '.'], '', $currencyString);
        $cleaned = str_replace(',', '.', $cleaned);

        return (float) $cleaned;
    }
}
