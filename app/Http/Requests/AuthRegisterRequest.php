<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class AuthRegisterRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'phone' => ['required', 'string', 'max:255', 'unique:users,phone'],
            'document' => ['required', 'string', 'max:255', 'unique:users,document'],
            'document_type' => ['required', 'string', 'in:cpf,cnpj'],
            'agreement_terms' => ['nullable', 'boolean'],

            // Documentos para PF
            'document_front' => ['required_if:document_type,cpf', 'file', 'max:5120', 'mimes:jpeg,png,pdf'],
            'document_back' => ['required_if:document_type,cpf', 'file', 'max:5120', 'mimes:jpeg,png,pdf'],
            'selfie' => ['required_if:document_type,cpf', 'file', 'max:5120', 'mimes:jpeg,png'],

            // Campos para PJ
            'document_company' => ['required_if:document_type,cnpj', 'string', 'max:255'],
            'company_legal_name' => ['required_if:document_type,cnpj', 'string', 'max:255'],
            'company_trading_name' => ['required_if:document_type,cnpj', 'string', 'max:255'],

            // Documentos para PJ
            'company_contract' => ['required_if:document_type,cnpj', 'file', 'max:5120', 'mimes:jpeg,png,pdf'],
            'company_statute' => ['required_if:document_type,cnpj', 'file', 'max:5120', 'mimes:jpeg,png,pdf'],
            'company_proxy' => ['required_if:document_type,cnpj', 'file', 'max:5120', 'mimes:jpeg,png,pdf'],
        ];
    }

    public function messages(): array
    {
        return [
            'first_name.required' => 'O nome é obrigatório.',
            'last_name.required' => 'O sobrenome é obrigatório.',
            'email.required' => 'O email é obrigatório.',
            'email.email' => 'O email deve ser um email válido.',
            'email.unique' => 'O email já está em uso.',
            'phone.required' => 'O telefone é obrigatório.',
            'phone.unique' => 'O telefone já está em uso.',
            'document.required' => 'O documento é obrigatório.',
            'document.unique' => 'O documento já está em uso.',
            'document_type.required' => 'O tipo de documento é obrigatório.',
            'document_type.in' => 'O tipo de documento deve ser CPF ou CNPJ.',
            'agreement_terms.boolean' => 'O campo de aceite dos termos deve ser verdadeiro ou falso.',

            // Mensagens para documentos PF
            'document_front.required_if' => 'A foto da frente do documento é obrigatória quando o tipo de documento for CPF.',
            'document_front.file' => 'A foto da frente do documento deve ser um arquivo.',
            'document_front.max' => 'A foto da frente do documento deve ter no máximo 5MB.',
            'document_front.mimes' => 'A foto da frente do documento deve ser JPG, PNG ou PDF.',
            'document_back.required_if' => 'A foto do verso do documento é obrigatória quando o tipo de documento for CPF.',
            'document_back.file' => 'A foto do verso do documento deve ser um arquivo.',
            'document_back.max' => 'A foto do verso do documento deve ter no máximo 5MB.',
            'document_back.mimes' => 'A foto do verso do documento deve ser JPG, PNG ou PDF.',
            'selfie.required' => 'A selfie é obrigatória.',
            'selfie.file' => 'A selfie deve ser um arquivo.',
            'selfie.max' => 'A selfie deve ter no máximo 5MB.',
            'selfie.mimes' => 'A selfie deve ser JPG ou PNG.',

            // Mensagens para campos PJ
            'company_legal_name.required_if' => 'O nome legal da empresa é obrigatório quando o tipo de documento for CNPJ.',
            'company_trading_name.required_if' => 'O nome comercial da empresa é obrigatório quando o tipo de documento for CNPJ.',
            'document_company.required_if' => 'O CNPJ da empresa é obrigatório quando o tipo de documento for CNPJ.',

            // Mensagens para documentos PJ
            'company_contract.required_if' => 'O contrato social é obrigatório quando o tipo de documento for CNPJ.',
            'company_contract.file' => 'O contrato social deve ser um arquivo.',
            'company_contract.max' => 'O contrato social deve ter no máximo 5MB.',
            'company_contract.mimes' => 'O contrato social deve ser JPG, PNG ou PDF.',
            'company_statute.required_if' => 'O estatuto social é obrigatório quando o tipo de documento for CNPJ.',
            'company_statute.file' => 'O estatuto social deve ser um arquivo.',
            'company_statute.max' => 'O estatuto social deve ter no máximo 5MB.',
            'company_statute.mimes' => 'O estatuto social deve ser JPG, PNG ou PDF.',
            'company_proxy.required_if' => 'A procuração é obrigatória quando o tipo de documento for CNPJ.',
            'company_proxy.file' => 'A procuração deve ser um arquivo.',
            'company_proxy.max' => 'A procuração deve ter no máximo 5MB.',
            'company_proxy.mimes' => 'A procuração deve ser JPG, PNG ou PDF.',
        ];
    }
}
