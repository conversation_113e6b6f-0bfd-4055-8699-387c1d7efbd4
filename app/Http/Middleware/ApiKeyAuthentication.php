<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\ApiKey;
use Symfony\Component\HttpFoundation\Response;

class ApiKeyAuthentication
{
    public function handle(Request $request, Closure $next): Response
    {
        $apiKey = $request->header('api_key');
        $apiSecret = $request->header('api_secret');

        if (!$apiKey || !$apiSecret) {
            return response()->json([
                'message' => 'API Key e Secret são obrigatórios'
            ], 401);
        }

        $key = ApiKey::where('key', $apiKey)
            ->where('secret', $apiSecret)
            ->where('status', 'active')
            ->first();
        if (!$key) {
            return response()->json([
                'message' => 'API Key ou Secret inválidos'
            ], 401);
        }

        $request->merge(['auth_user' => $key->user]);

        return $next($request);
    }
}
