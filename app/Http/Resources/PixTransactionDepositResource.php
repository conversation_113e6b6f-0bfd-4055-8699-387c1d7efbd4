<?php

namespace App\Http\Resources;

use App\Traits\MonetaryConversion;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PixTransactionDepositResource extends JsonResource
{
    use MonetaryConversion;

    public function toArray(Request $request): array
    {
        return [
            'payment_company' => 'Global Pay',
            'transaction_id_external' => $this->transaction_id_external,
            'beneficiary' => $this->user->first_name . " " . $this->user->last_name,
            'document_beneficiary' => $this->document_beneficiary,
            'pix_key' => $this->pix_key,
            'amount' => $this->convertToReais($this->amount),
            'status' => $this->status,
            'type' => $this->type,
            'qr_code' => $this->qr_code,
            'qr_code_copy_paste' => $this->qr_code,
            'created_at' => $this->created_at,
        ];
    }
}
