<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'email' => $this->email,
            'phone' => $this->phone,
            'document' => $this->document,
            'company_name' => $this->company_name,
            'role' => $this->role,
            'status' => $this->status,
            'email_verified_at' => $this->email_verified_at,
            'kyc_status' => $this->kyc_status,
            'kyc_verified_at' => $this->kyc_verified_at,
            'kyc_rejection_reason' => $this->kyc_rejection_reason,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'pix_accounts' => $this->whenLoaded('pixAccounts'),
            'api_keys' => $this->whenLoaded('apiKeys'),
        ];
    }
}
