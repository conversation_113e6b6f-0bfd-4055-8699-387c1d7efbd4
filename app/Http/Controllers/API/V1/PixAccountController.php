<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Services\PixAccountService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use OpenApi\Annotations as OA;

class PixAccountController extends Controller
{
    public function __construct(
        private PixAccountService $pixAccountService
    ) {}

    /**
     * @OA\Get(
     *     path="/api/v1/pix-accounts/{id}/balance",
     *     summary="Consultar saldo da conta PIX",
     *     tags={"Conta"},
     *     security={{"apiKey":{}, "apiSecret":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="ID da conta PIX",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Saldo retornado com sucesso",
     *         @OA\JsonContent(
     *             @OA\Property(property="balance", type="number", format="float")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Conta PIX não encontrada"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erro ao consultar saldo"
     *     )
     * )
     */
    public function balance(Request $request, int $id): JsonResponse
    {
        try {
            $account = $this->pixAccountService->findByPixAccountId($request['auth_user'], $id);
            if (!$account) {
                return response()->json(['message' => 'Conta PIX não encontrada'], 404);
            }
            $balance = $this->pixAccountService->getBalance($account);
            return response()->json(['balance' => $balance]);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao buscar saldo da conta PIX: ' . $e->getMessage()], 500);
        }
    }

    /**
     * @OA\Patch(
     *     path="/api/v1/pix-accounts/webhook",
     *     summary="Cadastrar ou atualizar webhook",
     *     description="Configura a URL do webhook para receber notificações de transações e mudanças de status KYC",
     *     tags={"Webhooks"},
     *     security={{"apiKey":{}, "apiSecret":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"webhook_url"},
     *             @OA\Property(
     *                 property="webhook_url",
     *                 type="string",
     *                 format="url",
     *                 example="https://meusite.com/webhook",
     *                 description="URL do endpoint que receberá as notificações webhook"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Webhook configurado com sucesso",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Webhook atualizado com sucesso")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Erro de validação",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="O campo webhook_url é obrigatório."),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="webhook_url",
     *                     type="array",
     *                     @OA\Items(type="string", example="O campo webhook_url deve ser uma URL válida.")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erro interno do servidor",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Erro ao criar webhook: ...")
     *         )
     *     )
     * )
     */
    public function storeWebhook(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'webhook_url' => 'required|string',
        ]);

        try {
            $webhook = $this->pixAccountService->createWebhook($request['auth_user'], $validated);
            if (!$webhook) {
                return response()->json(['message' => 'Erro ao criar webhook'], 500);
            }
            return response()->json(['message' => 'Webhook atualizado com sucesso']);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao criar webhook: ' . $e->getMessage()], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/pix-accounts/webhook",
     *     summary="Consultar webhook configurado",
     *     description="Retorna a URL do webhook atualmente configurada para a conta",
     *     tags={"Webhooks"},
     *     security={{"apiKey":{}, "apiSecret":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Webhook retornado com sucesso",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="webhook_url",
     *                 type="string",
     *                 format="url",
     *                 example="https://meusite.com/webhook",
     *                 description="URL do webhook configurada (null se não configurada)"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Não autorizado",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Unauthenticated")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erro interno do servidor",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Erro interno do servidor")
     *         )
     *     )
     * )
     */
    public function getWebhook(Request $request): JsonResponse
    {
        $webhook = $request['auth_user']->webhook_url;
        return response()->json(['webhook_url' => $webhook]);
    }

    /**
     * @OA\Info(
     *     title="Eventos Webhook",
     *     description="
     * ## Tipos de Eventos Webhook Enviados
     *
     * O sistema envia os seguintes tipos de eventos webhook para a URL configurada:
     *
     * ### 1. transaction.created
     * Enviado quando uma nova transação PIX é criada (depósito ou saque)
     *
     * **Payload exemplo:**
     * ```json
     * {
     *   'event_type': 'transaction.created',
     *   'payload': {
     *     'transaction_id': 'TXN123456',
     *     'order_id': 'ORD789',
     *     'type': 'deposit',
     *     'amount': 100.50,
     *     'status': 'pending_payment',
     *     'qr_code': 'PIX_QR_CODE_STRING',
     *     'created_at': '2024-01-01T12:00:00Z'
     *   },
     *   'timestamp': '2024-01-01T12:00:00Z',
     *   'webhook_id': 123
     * }
     * ```
     *
     * ### 2. transaction.completed
     * Enviado quando uma transação PIX é finalizada com sucesso
     *
     * **Payload exemplo:**
     * ```json
     * {
     *   'event_type': 'transaction.completed',
     *   'payload': {
     *     'transaction_id': 'TXN123456',
     *     'type': 'deposit',
     *     'amount': 100.50,
     *     'status': 'completed',
     *     'completed_at': '2024-01-01T12:15:00Z'
     *   },
     *   'timestamp': '2024-01-01T12:15:00Z',
     *   'webhook_id': 124
     * }
     * ```
     *
     * ### 3. kyc.status_changed
     * Enviado quando o status do KYC do usuário é alterado
     *
     * **Payload exemplo:**
     * ```json
     * {
     *   'event_type': 'kyc.status_changed',
     *   'payload': {
     *     'user_id': 1,
     *     'kyc_status': 'approved',
     *     'document_type': 'cpf',
     *     'changed_at': '2024-01-01T12:30:00Z'
     *   },
     *   'timestamp': '2024-01-01T12:30:00Z',
     *   'webhook_id': 125
     * }
     * ```
     *
     * ## Configurações de Entrega
     *
     * - **Timeout**: 30 segundos por tentativa
     * - **Tentativas**: Máximo 3 tentativas automáticas
     * - **Retry**: Intervalo exponencial entre tentativas
     * - **Assinatura**: Webhooks são enviados via POST com Content-Type application/json
     *
     * ## Códigos de Status Esperados
     *
     * - **2xx**: Webhook entregue com sucesso
     * - **4xx/5xx**: Webhook será tentado novamente até o limite de tentativas
     *
     * "
     * )
     */
}
