<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Services\ApiKeyService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use OpenApi\Annotations as OA;

class ApiKeyController extends Controller
{
    public function __construct(
        private ApiKeyService $apiKeyService
    ) {}

    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
        ]);

        try {
            $apiKey = $this->apiKeyService->create($request->user(), $validated);
            return response()->json($apiKey, 201);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao criar API Key: ' . $e->getMessage()], 500);
        }
    }

    public function index(Request $request): JsonResponse
    {
        try {
            $apiKeys = $this->apiKeyService->list($request->user());
            return response()->json($apiKeys);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao listar API Keys: ' . $e->getMessage()], 500);
        }
    }

    public function show(Request $request, int $id): JsonResponse
    {
        try {
            $apiKey = $this->apiKeyService->find($request->user(), $id);

            if (!$apiKey) {
                return response()->json(['message' => 'API Key não encontrada'], 404);
            }

            return response()->json($apiKey);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao buscar API Key: ' . $e->getMessage()], 500);
        }
    }

    public function deactivate(Request $request, int $id): JsonResponse
    {
        try {
            $apiKey = $this->apiKeyService->find($request->user(), $id);

            if (!$apiKey) {
                return response()->json(['message' => 'API Key não encontrada'], 404);
            }

            $deactivated = $this->apiKeyService->deactivate($apiKey);

            if (!$deactivated) {
                return response()->json(['message' => 'Erro ao desativar API Key'], 500);
            }

            return response()->json(['message' => 'API Key desativada com sucesso']);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao desativar API Key: ' . $e->getMessage()], 500);
        }
    }
}
