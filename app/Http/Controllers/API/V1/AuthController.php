<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\AuthLoginRequest;
use App\Http\Requests\AuthRegisterRequest;
use App\Http\Requests\ChangePasswordRequest;
use App\Services\AuthService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use OpenApi\Annotations as OA;

class AuthController extends Controller
{
    public function __construct(
        protected AuthService $authService
    ) {}

    /**
     * @OA\Post(
     *     path="/api/v1/auth/register",
     *     summary="Registrar novo usuário com KYC",
     *     description="Registra um novo usuário (PF ou PJ) e faz upload dos documentos para KYC em uma única requisição",
     *     tags={"Cadastro"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 required={
     *                     "first_name",
     *                     "last_name",
     *                     "email",
     *                     "password",
     *                     "password_confirmation",
     *                     "phone",
     *                     "document",
     *                     "document_type",
     *                     "agreement_terms",
     *                     "selfie"
     *                 },
     *                 @OA\Property(property="first_name", type="string", example="João"),
     *                 @OA\Property(property="last_name", type="string", example="Silva"),
     *                 @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
     *                 @OA\Property(property="password", type="string", format="password", example="senha123"),
     *                 @OA\Property(property="password_confirmation", type="string", format="password", example="senha123"),
     *                 @OA\Property(property="phone", type="string", example="11999999999"),
     *                 @OA\Property(property="document", type="string", example="12345678900", description="CPF do responsável legal para PJ ou CPF para PF"),
     *                 @OA\Property(property="document_type", type="string", enum={"cpf", "cnpj"}, example="cpf"),
     *                 @OA\Property(property="agreement_terms", type="boolean", example=true),
     *                 @OA\Property(property="company_legal_name", type="string", example="Empresa XYZ Ltda", description="Obrigatório para PJ"),
     *                 @OA\Property(property="company_trading_name", type="string", example="Empresa XYZ", description="Obrigatório para PJ"),
          *                 @OA\Property(property="company_document", type="string", example="12345678901234", description="CNPJ da empresa - Obrigatório para PJ"),
     *                 @OA\Property(
     *                     property="document_front",
     *                     type="string",
     *                     format="binary",
     *                     description="Foto da frente do documento (RG/CNH) - Obrigatório para PF, JPG, PNG ou PDF, máximo 5MB"
     *                 ),
     *                 @OA\Property(
     *                     property="document_back",
     *                     type="string",
     *                     format="binary",
     *                     description="Foto do verso do documento (RG/CNH) - Obrigatório para PF, JPG, PNG ou PDF, máximo 5MB"
          *                 ),
     *                 @OA\Property(
     *                     property="company_contract",
     *                     type="string",
     *                     format="binary",
     *                     description="Contrato Social - Obrigatório para PJ, JPG, PNG ou PDF, máximo 5MB"
     *                 ),
     *                 @OA\Property(
     *                     property="company_statute",
     *                     type="string",
     *                     format="binary",
     *                     description="Estatuto Social - Obrigatório para PJ, JPG, PNG ou PDF, máximo 5MB"
     *                 ),
     *                 @OA\Property(
     *                     property="company_proxy",
     *                     type="string",
     *                     format="binary",
     *                     description="Procuração - Obrigatório para PJ, JPG, PNG ou PDF, máximo 5MB"
          *                 ),
     *                 @OA\Property(
     *                     property="selfie",
     *                     type="string",
     *                     format="binary",
     *                     description="Selfie segurando o documento - Obrigatório para todos, JPG ou PNG, máximo 5MB"
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Usuário registrado com sucesso",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="user",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="first_name", type="string", example="João"),
     *                 @OA\Property(property="last_name", type="string", example="Silva"),
     *                 @OA\Property(property="email", type="string", example="<EMAIL>"),
     *                 @OA\Property(property="phone", type="string", example="11999999999"),
     *                 @OA\Property(property="document", type="string", example="12345678900"),
     *                 @OA\Property(property="document_type", type="string", example="cpf"),
     *                 @OA\Property(property="company_legal_name", type="string", example="Empresa XYZ Ltda"),
     *                 @OA\Property(property="company_trading_name", type="string", example="Empresa XYZ"),
     *                 @OA\Property(property="company_document", type="string", example="12345678901234"),
     *                 @OA\Property(property="kyc_status", type="string", example="under_review"),
     *                 @OA\Property(property="created_at", type="string", format="date-time"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time")
     *             ),
     *             @OA\Property(property="credentials", type="object",
     *                 @OA\Property(property="api_key", type="string", example="pk_live_123456..."),
     *                 @OA\Property(property="api_secret", type="string", example="sk_live_123456...")
     *             ),
     *             @OA\Property(property="kyc_status", type="string", example="under_review")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Erro de validação",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Os dados fornecidos são inválidos."),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="document_front",
     *                     type="array",
     *                     @OA\Items(type="string", example="A foto da frente do documento é obrigatória")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erro ao registrar usuário",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Erro ao processar upload dos documentos")
     *         )
     *     )
     * )
     */
    public function register(AuthRegisterRequest $request): JsonResponse
    {
        try {
            $result = $this->authService->registerPixAccount($request->all());
            return response()->json($result, Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao registrar usuário: ' . $e->getMessage()], 500);
        }
    }


    public function login(AuthLoginRequest $request): JsonResponse
    {
        try {
            $result = $this->authService->login($request->only(['email', 'password']));
            return response()->json([
                'user' => $result['user'],
                'access_token' => $result['access_token'],
                'token_type' => $result['token_type'],
            ]);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao fazer login: ' . $e->getMessage()], 401);
        }
    }


    public function logout(Request $request): JsonResponse
    {
        try {
            $this->authService->logout($request);
            return response()->json(['message' => 'Você saiu com sucesso!']);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao fazer logout: ' . $e->getMessage()], 500);
        }
    }


    public function me(Request $request): JsonResponse
    {
        try {
            $user = $this->authService->me($request);
            return response()->json(['user' => $user]);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao buscar dados do usuário: ' . $e->getMessage()], 500);
        }
    }
}
