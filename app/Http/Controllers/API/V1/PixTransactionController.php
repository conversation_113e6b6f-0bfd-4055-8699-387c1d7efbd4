<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Services\PixTransactionService;
use App\Services\PixAccountService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use OpenApi\Annotations as OA;

class PixTransactionController extends Controller
{
    public function __construct(
        private PixTransactionService $pixTransactionService,
        private PixAccountService $pixAccountService
    ) {}

    public function findByExternalId(Request $request, string $externalId): JsonResponse
    {
        try {
            $transaction = $this->pixTransactionService->findByExternalId($externalId);

            if (!$transaction) {
                return response()->json(['message' => 'Transação PIX não encontrada'], 404);
            }

            return response()->json($transaction);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao buscar transação PIX: ' . $e->getMessage()], 500);
        }
    }

    /**
     * @OA\Post(
     *     path="/api/v1/pix-transactions/deposit",
     *     summary="Realizar depósito PIX",
     *     tags={"Transações"},
     *     security={{"apiKey":{}, "apiSecret":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"pix_account_id", "amount", "description"},
     *             @OA\Property(property="pix_account_id", type="integer", description="ID da conta PIX de destino"),
     *             @OA\Property(property="amount", type="number", format="float", description="Valor do depósito"),
     *             @OA\Property(property="description", type="string", maxLength=1000),
     *             @OA\Property(property="qr_code", type="string", description="QR Code do PIX (opcional)")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Depósito PIX iniciado com sucesso",
     *         @OA\JsonContent(
     *             @OA\Property(property="transaction", type="object"),
     *             @OA\Property(property="qr_code", type="string"),
     *             @OA\Property(property="pix_key", type="string")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Conta PIX não encontrada"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erro ao iniciar depósito PIX"
     *     )
     * )
     */
    public function deposit(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'amount' => 'required|numeric|min:0.01',
            'document_beneficiary' => 'required|string|max:255',
            'name' => 'required|string|max:255',
        ]);

        $account = $this->pixAccountService->findByPixAccountId($request['auth_user']);

        if (!$account) {
            return response()->json(['message' => 'Conta PIX não encontrada'], 404);
        }

        try {
            $result = $this->pixTransactionService->createDeposit($request['auth_user'], $account, $validated);
            return response()->json($result, 201);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao iniciar depósito PIX: ' . $e->getMessage()], 500);
        }
    }

    /**
     * @OA\Post(
     *     path="/api/v1/pix-transactions/withdraw",
     *     summary="Realizar saque PIX",
     *     tags={"Transações"},
     *     security={{"apiKey":{}, "apiSecret":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"pix_account_id", "amount", "pix_key"},
     *             @OA\Property(property="pix_account_id", type="integer", description="ID da conta PIX de origem"),
     *             @OA\Property(property="amount", type="number", format="float", description="Valor do saque"),
     *             @OA\Property(property="description", type="string", maxLength=1000),
     *             @OA\Property(property="pix_key", type="string", description="Chave PIX do destinatário")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Saque PIX iniciado com sucesso",
     *         @OA\JsonContent(
     *             @OA\Property(property="transaction", type="object"),
     *             @OA\Property(property="status", type="string")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Conta PIX não encontrada"
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Saldo insuficiente"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erro ao iniciar saque PIX"
     *     )
     * )
     */
    public function withdraw(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'amount' => 'required|numeric|min:0.01',
            'pix_key' => 'required|string|max:255',
            'document_beneficiary' => 'required|string|max:255',
        ]);

        $account = $this->pixAccountService->findByPixAccountId($request['auth_user']);

        if (!$account) {
            return response()->json(['message' => 'Conta PIX não encontrada'], 404);
        }

        try {
            $result = $this->pixTransactionService->createWithdraw($request['auth_user'], $account, $validated);
            return response()->json($result, 200);
        } catch (\Exception $e) {
            if ($e->getMessage() === 'Saldo insuficiente para realizar o saque') {
                return response()->json(['error' => $e->getMessage()], 400);
            }
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/pix-transactions",
     *     summary="Listar transações PIX da conta",
     *     description="Lista todas as transações PIX da conta autenticada com filtros opcionais",
     *     tags={"Transações"},
     *     security={{"apiKey":{}, "apiSecret":{}}},
     *     @OA\Parameter(
     *         name="status",
     *         in="query",
     *         required=false,
     *         description="Filtrar por status (pending, pending_payment, received, completed, failed, cancelled)",
     *         @OA\Schema(type="string", enum={"pending", "pending_payment", "received", "completed", "failed", "cancelled"})
     *     ),
     *     @OA\Parameter(
     *         name="type",
     *         in="query",
     *         required=false,
     *         description="Filtrar por tipo (deposit, withdraw)",
     *         @OA\Schema(type="string", enum={"deposit", "withdraw"})
     *     ),
     *     @OA\Parameter(
     *         name="date_from",
     *         in="query",
     *         required=false,
     *         description="Data inicial (formato: Y-m-d)",
     *         @OA\Schema(type="string", format="date", example="2024-01-01")
     *     ),
     *     @OA\Parameter(
     *         name="date_to",
     *         in="query",
     *         required=false,
     *         description="Data final (formato: Y-m-d)",
     *         @OA\Schema(type="string", format="date", example="2024-12-31")
     *     ),
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         required=false,
     *         description="Número da página para paginação",
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Lista de transações retornada com sucesso",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="transaction_id_external", type="string", example="TXN123456"),
     *                     @OA\Property(property="order_id", type="string", example="ORD789"),
     *                     @OA\Property(property="type", type="string", example="deposit"),
     *                     @OA\Property(property="amount", type="number", format="float", example=100.50),
     *                     @OA\Property(property="status", type="string", example="completed"),
     *                     @OA\Property(property="description", type="string", example="Depósito PIX"),
     *                     @OA\Property(property="pix_key", type="string", example="<EMAIL>"),
     *                     @OA\Property(property="qr_code", type="string", example="PIX_QR_CODE_STRING"),
     *                     @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-01T12:00:00Z"),
     *                     @OA\Property(property="processed_at", type="string", format="date-time", example="2024-01-01T12:15:00Z")
     *                 )
     *             ),
     *             @OA\Property(property="current_page", type="integer", example=1),
     *             @OA\Property(property="last_page", type="integer", example=5),
     *             @OA\Property(property="per_page", type="integer", example=20),
     *             @OA\Property(property="total", type="integer", example=95)
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Não autorizado",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Unauthenticated")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erro interno do servidor",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Erro ao listar transações: ...")
     *         )
     *     )
     * )
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $filters = $request->only(['status', 'type', 'date_from', 'date_to']);
            $transactions = $this->pixTransactionService->list($request['auth_user'], $filters);

            return response()->json($transactions);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Erro ao listar transações: ' . $e->getMessage()], 500);
        }
    }
}
