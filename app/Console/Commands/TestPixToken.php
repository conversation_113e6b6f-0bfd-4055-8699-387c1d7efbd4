<?php

namespace App\Console\Commands;

use App\Adapters\ApiPixAdapter;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class TestPixToken extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pix:test-token {--details : Mostrar informações detalhadas}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Testa a geração do token da API PIX Sicredi';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔄 Iniciando teste de geração do token PIX...');
        $this->newLine();

        try {
            // Verifica se as configurações estão presentes
            $this->checkConfiguration();

            // Instancia o adapter
            $adapter = new ApiPixAdapter();

            $this->info('📝 Gerando token...');

            // Mede o tempo de geração do token
            $startTime = microtime(true);
            $token = $adapter->getToken();
            $endTime = microtime(true);

            $executionTime = round(($endTime - $startTime) * 1000, 2);

            $this->newLine();
            $this->info('✅ Token gerado com sucesso!');
            $this->info("⏱️  Tempo de execução: {$executionTime}ms");

            if ($this->option('details')) {
                $this->newLine();
                $this->info('🔍 Informações detalhadas:');
                $this->line("  Token (primeiros 20 caracteres): " . substr($token, 0, 20) . '...');
                $this->line("  Tamanho do token: " . strlen($token) . ' caracteres');
                $this->line("  Token válido: " . ($adapter->isTokenValid() ? 'Sim' : 'Não'));
            }

            // Teste adicional: limpar token e verificar se foi limpo
            $this->newLine();
            $this->info('🧹 Testando limpeza de token...');
            $adapter->clearToken();

            if (!$adapter->isTokenValid()) {
                $this->info('✅ Token limpo com sucesso!');
            } else {
                $this->error('❌ Erro ao limpar token!');
            }

            $this->newLine();
            $this->info('🎉 Teste concluído com sucesso!');
        } catch (\Exception $e) {
            $this->newLine();
            $this->error('❌ Erro ao gerar token:');
            $this->error("   {$e->getMessage()}");

            if ($this->option('details')) {
                $this->newLine();
                $this->error('🔍 Stack trace:');
                $this->error($e->getTraceAsString());
            }

            $this->newLine();
            $this->error('💡 Dicas para resolver:');
            $this->line('  - Verifique se todas as variáveis de ambiente estão configuradas');
            $this->line('  - Confirme se os certificados estão no caminho correto');
            $this->line('  - Teste a conectividade com a API Sicredi');

            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }

    /**
     * Verifica se as configurações necessárias estão presentes
     */
    private function checkConfiguration(): void
    {
        $this->info('🔧 Verificando configurações...');

        $requiredEnvs = [
            'API_SICREDI_CERT_PATH',
            'API_SICREDI_KEY_PATH',
            'API_SICREDI_CLIENT_ID',
            'API_SICREDI_CLIENT_SECRET',
            'API_SICREDI_CONTA',
            'API_SICREDI_COOPERATIVA',
            'API_SICREDI_DOCUMENTO',
            'API_SICREDI_RECEBIMENTOS_BASE_URL'
        ];

        $missingConfigs = [];

        foreach ($requiredEnvs as $env) {
            if (empty(env($env))) {
                $missingConfigs[] = $env;
            }
        }

        if (!empty($missingConfigs)) {
            $this->error('❌ Configurações faltando no .env:');
            foreach ($missingConfigs as $config) {
                $this->line("  - {$config}");
            }
            throw new \RuntimeException('Configurações obrigatórias não encontradas');
        }

        // Verifica se os arquivos de certificado existem
        $certPath = env('API_SICREDI_CERT_PATH');
        $keyPath = env('API_SICREDI_KEY_PATH');

        if (!str_starts_with($certPath, '/')) {
            $certPath = base_path($certPath);
        }

        if (!str_starts_with($keyPath, '/')) {
            $keyPath = base_path($keyPath);
        }

        if (!file_exists($certPath)) {
            throw new \RuntimeException("Arquivo de certificado não encontrado: {$certPath}");
        }

        if (!file_exists($keyPath)) {
            throw new \RuntimeException("Arquivo de chave não encontrado: {$keyPath}");
        }

        $this->info('✅ Todas as configurações estão presentes!');
    }
}
