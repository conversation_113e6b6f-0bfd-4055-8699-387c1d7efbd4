<?php

namespace App\Console\Commands;

use App\Models\WebhookEvent;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;

class CleanupWebhookEvents extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'webhook:cleanup {--days=7 : Dias para manter os webhooks entregues} {--failed-days=30 : Dias para manter os webhooks que falharam}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Limpa webhooks antigos do banco de dados para otimizar performance';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = (int) $this->option('days');
        $failedDays = (int) $this->option('failed-days');

        $this->info("Iniciando limpeza de webhooks...");

        // Limpar webhooks entregues com sucesso (mais antigos que X dias)
        $deliveredCutoff = Carbon::now()->subDays($days);
        $deliveredCount = WebhookEvent::delivered()
            ->where('delivered_at', '<', $deliveredCutoff)
            ->count();

        if ($deliveredCount > 0) {
            $this->line("Encontrados {$deliveredCount} webhooks entregues para limpeza (mais antigos que {$days} dias)");

            if ($this->confirm('Deseja continuar com a limpeza?')) {
                $deleted = WebhookEvent::delivered()
                    ->where('delivered_at', '<', $deliveredCutoff)
                    ->delete();

                $this->info("✅ {$deleted} webhooks entregues foram removidos.");
            }
        } else {
            $this->info("Nenhum webhook entregue encontrado para limpeza.");
        }

        // Limpar webhooks que falharam (mais antigos que Y dias)
        $failedCutoff = Carbon::now()->subDays($failedDays);
        $failedCount = WebhookEvent::failed()
            ->where('last_attempt_at', '<', $failedCutoff)
            ->count();

        if ($failedCount > 0) {
            $this->line("Encontrados {$failedCount} webhooks que falharam para limpeza (mais antigos que {$failedDays} dias)");

            if ($this->confirm('Deseja continuar com a limpeza dos webhooks que falharam?')) {
                $deleted = WebhookEvent::failed()
                    ->where('last_attempt_at', '<', $failedCutoff)
                    ->delete();

                $this->info("✅ {$deleted} webhooks que falharam foram removidos.");
            }
        } else {
            $this->info("Nenhum webhook que falhou encontrado para limpeza.");
        }

        // Mostrar estatísticas atuais
        $this->showStats();

        $this->info("Limpeza de webhooks concluída!");
    }

    private function showStats(): void
    {
        $stats = [
            'Pendentes' => WebhookEvent::pending()->count(),
            'Entregues' => WebhookEvent::delivered()->count(),
            'Falharam' => WebhookEvent::failed()->count(),
            'Total' => WebhookEvent::count(),
        ];

        $this->newLine();
        $this->info("📊 Estatísticas atuais de webhooks:");

        foreach ($stats as $status => $count) {
            $this->line("  {$status}: {$count}");
        }
    }
}
