<?php

namespace App\Console\Commands;

use App\Models\WebhookEvent;
use App\Jobs\ProcessWebhookEvent;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;

class RetryFailedWebhooks extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'webhook:retry {--hours=1 : Horas desde a última tentativa} {--max-attempts=3 : Máximo de tentativas} {--limit=50 : Limite de webhooks a reprocessar}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Retenta webhooks que falharam há X horas';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $hours = (int) $this->option('hours');
        $maxAttempts = (int) $this->option('max-attempts');
        $limit = (int) $this->option('limit');

        $this->info("Buscando webhooks que falharam há {$hours} horas...");

        $cutoff = Carbon::now()->subHours($hours);

        $failedWebhooks = WebhookEvent::failed()
            ->where('attempts', '<', $maxAttempts)
            ->where('last_attempt_at', '<', $cutoff)
            ->limit($limit)
            ->get();

        if ($failedWebhooks->isEmpty()) {
            $this->info("Nenhum webhook encontrado para reprocessamento.");
            return;
        }

        $this->info("Encontrados {$failedWebhooks->count()} webhooks para reprocessamento.");

        if (!$this->confirm('Deseja reprocessar estes webhooks?')) {
            $this->info("Operação cancelada.");
            return;
        }

        $this->output->progressStart($failedWebhooks->count());

        $requeued = 0;

        foreach ($failedWebhooks as $webhook) {
            try {
                // Reset status para pending para permitir reprocessamento
                $webhook->update([
                    'status' => 'pending',
                    'error_message' => null
                ]);

                // Dispatcha o job novamente
                ProcessWebhookEvent::dispatch($webhook);
                $requeued++;

                $this->output->progressAdvance();
            } catch (\Exception $e) {
                $this->error("Erro ao reprocessar webhook {$webhook->id}: {$e->getMessage()}");
            }
        }

        $this->output->progressFinish();

        $this->info("✅ {$requeued} webhooks foram enviados para reprocessamento.");

        // Mostrar estatísticas
        $this->showStats();
    }

    private function showStats(): void
    {
        $stats = [
            'Pendentes' => WebhookEvent::pending()->count(),
            'Entregues' => WebhookEvent::delivered()->count(),
            'Falharam' => WebhookEvent::failed()->count(),
            'Total' => WebhookEvent::count(),
        ];

        $this->newLine();
        $this->info("📊 Estatísticas atuais de webhooks:");

        foreach ($stats as $status => $count) {
            $this->line("  {$status}: {$count}");
        }
    }
}
