<?php

namespace App\Adapters;

use App\Traits\MonetaryConversion;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Log;

class MultPagAdapter
{
    use MonetaryConversion;

    private const MULT_PAG_SCOPE = 'multipag.boleto.pagar multipag.boleto.consultar multipag.tributos.pagar multipag.tributos.consultar multipag.pix.pagar multipag.pix.consultar';

    private string $accessToken = '';
    private ?string $certPath;
    private ?string $keyPath;
    private ?string $clientId;
    private ?string $clientSecret;
    private ?string $conta;
    private ?string $cooperativa;
    private ?string $documento;
    private ?string $identificadorPagamentoAssociado;

    public function __construct()
    {
        $this->initializeConfiguration();
        $this->validateCertificates();
    }

    private function initializeConfiguration(): void
    {
        $this->certPath = env('API_SICREDI_CERT_PATH');
        $this->keyPath = env('API_SICREDI_KEY_PATH');
        $this->clientId = env('API_SICREDI_MULTIPAG_CLIENT_ID');
        $this->clientSecret = env('API_SICREDI_CLIENT_SECRET');
        $this->conta = env('API_SICREDI_CONTA');
        $this->cooperativa = env('API_SICREDI_COOPERATIVA');
        $this->documento = env('API_SICREDI_DOCUMENTO');
        $this->identificadorPagamentoAssociado = env('API_SICREDI_IDENTIFICADOR_PAGAMENTO_ASSOCIADO');

        if (!empty($this->certPath) && !str_starts_with($this->certPath, '/')) {
            $this->certPath = base_path($this->certPath);
        }

        if (!empty($this->keyPath) && !str_starts_with($this->keyPath, '/')) {
            $this->keyPath = base_path($this->keyPath);
        }

        $requiredConfigs = [
            'API_SICREDI_CERT_PATH' => $this->certPath,
            'API_SICREDI_KEY_PATH' => $this->keyPath,
            'API_SICREDI_MULTIPAG_CLIENT_ID' => $this->clientId,
            'API_SICREDI_CLIENT_SECRET' => $this->clientSecret,
            'API_SICREDI_CONTA' => $this->conta,
            'API_SICREDI_COOPERATIVA' => $this->cooperativa,
            'API_SICREDI_DOCUMENTO' => $this->documento,
            'API_SICREDI_IDENTIFICADOR_PAGAMENTO_ASSOCIADO' => $this->identificadorPagamentoAssociado,
        ];

        foreach ($requiredConfigs as $config => $value) {
            if (empty($value)) {
                throw new \RuntimeException("Configuração {$config} não encontrada no .env");
            }
        }
    }

    private function validateCertificates(): void
    {

        if (!file_exists($this->certPath)) {
            $errorMessage = "Certificado não encontrado em: {$this->certPath}";;
            throw new \RuntimeException($errorMessage);
        }

        if (!file_exists($this->keyPath)) {
            $errorMessage = "Chave privada não encontrada em: {$this->keyPath}";
            throw new \RuntimeException($errorMessage);
        }

        if (!is_readable($this->certPath)) {
            throw new \RuntimeException("Certificado não pode ser lido em: {$this->certPath}");
        }

        if (!is_readable($this->keyPath)) {
            throw new \RuntimeException("Chave privada não pode ser lida em: {$this->keyPath}");
        }
    }

    private function getHttpClient()
    {
        return Http::withOptions([
            'verify' => false,
            'cert' => $this->certPath,
            'ssl_key' => $this->keyPath,
            'timeout' => 30,
            'connect_timeout' => 10,
        ]);
    }

    public function getToken(): string
    {
        try {
            Log::info('MultPagAdapter: Iniciando geração de token');

            $response = $this->getHttpClient()
                ->asForm()
                ->post(env('MULT_PAG_BASE_URL') . '/thirdparty/auth/token', [
                    'grant_type' => 'client_credentials',
                    'client_id' => $this->clientId,
                    'client_secret' => $this->clientSecret,
                    'scope' => self::MULT_PAG_SCOPE
                ]);

            if (!$response->successful()) {
                $errorMessage = 'Erro ao gerar token: ' . $response->body();
                Log::error('MultPagAdapter: ' . $errorMessage, [
                    'status_code' => $response->status(),
                    'response_body' => $response->body()
                ]);
                throw new ConnectionException($errorMessage);
            }

            $data = $response->json();
            if (!isset($data['access_token'])) {
                throw new ConnectionException('Token de acesso não encontrado na resposta');
            }

            $this->accessToken = $data['access_token'];
            Log::info('MultPagAdapter: Token gerado com sucesso');

            return $this->accessToken;
        } catch (\Exception $e) {
            Log::error('MultPagAdapter: Erro ao gerar token', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    public function withdrawPixKey($transaction)
    {
        try {
            if (empty($this->accessToken)) {
                $this->getToken();
            }

            Log::info('MultPagAdapter: Iniciando pagamento PIX', [
                'transaction_id' => $transaction['transaction_id'] ?? 'N/A',
                'pix_key' => $transaction['pix_key'] ?? 'N/A'
            ]);

            // $payload = [
            //     'conta' => $this->conta,
            //     'cooperativa' => $this->cooperativa,
            //     'documento' => $this->documento,
            //     'identificadorPagamentoAssociado' => $this->identificadorPagamentoAssociado,

            //     // dados do pagamento
            //     'chavePix' => $transaction->pix_key,
            //     'documentoBeneficiario' => $transaction->document_beneficiary,
            //     'dataPagamento' => Carbon::now()->timezone('America/Sao_Paulo')->format('Y-m-d'),
            //     'valorPagamento' => $this->convertToReais($transaction->amount),
            //     'mensagemPix' => $transaction->message ?? 'Pagamento via PIX',
            //     'idTransacao' => $transaction->transaction_id_external
            // ];


            // sandbox
            $payload = [
                'conta' => $this->conta,
                'cooperativa' => $this->cooperativa,
                'documento' => $this->documento,

                // dados do pagamento
                'chavePix' => $transaction->pix_key,
                'documentoBeneficiario' => $transaction->document_beneficiary,
                'dataPagamento' => Carbon::now()->timezone('America/Sao_Paulo')->format('Y-m-d'),
                'valorPagamento' => $this->convertToReais($transaction->amount),
                'identificadorPagamentoAssociado' => $this->identificadorPagamentoAssociado,
                'mensagemPix' => 'Pagamento ordem 001',
                'idTransacao' => '0910F3HT1'
            ];

            $response = $this->getHttpClient()
                ->withHeaders([
                    'Authorization' => 'Bearer ' . $this->accessToken,
                    'Content-Type' => 'application/json',
                    'Accept' => '*/*'
                ])
                ->post(env('MULT_PAG_BASE_URL') . '/multipag-pagamento-sandbox/v1/pagamentos/pix/chave', $payload);

            if (!$response->successful()) {
                $errorMessage = 'Erro ao criar PIX: ' . $response->body();
                Log::error('MultPagAdapter: ' . $errorMessage, [
                    'status_code' => $response->status(),
                    'response_body' => $response->body(),
                    'payload' => $payload
                ]);
                throw new ConnectionException($errorMessage);
            }

            $responseData = $response->json();
            Log::info('MultPagAdapter: PIX criado com sucesso', [
                'transaction_id' => $transaction->transaction_id_external ?? 'N/A',
                'response' => $responseData
            ]);

            return $responseData;

        } catch (\Exception $e) {
            Log::error('MultPagAdapter: Erro ao processar PIX', [
                'error' => $e->getMessage(),
                'dados' => $transaction,
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    public function getPixTransaction(string $idTransacao): array
    {
        try {
            if (empty($this->accessToken)) {
                $this->getToken();
            }

            Log::info('MultPagAdapter: Consultando pagamento PIX', [
                'id_transacao' => $idTransacao
            ]);

            $response = $this->getHttpClient()
                ->withHeaders([
                    'Authorization' => 'Bearer ' . $this->accessToken,
                    'Content-Type' => 'application/json',
                    'x-conta' => $this->conta,
                    'x-cooperativa' => $this->cooperativa,
                    'x-documento' => $this->documento
                ])
                ->get(env('MULT_PAG_BASE_URL') . '/multipag-pagamento-sandbox/v1/pagamentos/pix/' . $idTransacao);

            if (!$response->successful()) {
                $errorMessage = 'Erro ao consultar PIX: ' . $response->body();
                Log::error('MultPagAdapter: ' . $errorMessage, [
                    'status_code' => $response->status(),
                    'response_body' => $response->body(),
                    'id_transacao' => $idTransacao
                ]);
                throw new ConnectionException($errorMessage);
            }

            $responseData = $response->json();
            Log::info('MultPagAdapter: PIX consultado com sucesso', [
                'id_transacao' => $idTransacao,
                'response' => $responseData
            ]);

            return $responseData;

        } catch (\Exception $e) {
            Log::error('MultPagAdapter: Erro ao consultar PIX', [
                'error' => $e->getMessage(),
                'id_transacao' => $idTransacao,
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    public function isTokenValid(): bool
    {
        return !empty($this->accessToken);
    }

    public function clearToken(): void
    {
        $this->accessToken = '';
    }
}
