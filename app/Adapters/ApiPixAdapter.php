<?php

namespace App\Adapters;

use App\Traits\MonetaryConversion;
use Carbon\Carbon;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ApiPixAdapter
{
    use MonetaryConversion;

    private const API_PIX_SCOPE = 'cob.write+cob.read+webhook.read+webhook.write';

    private string $accessToken = '';
    private string $certPath;
    private string $keyPath;
    private string $clientId;
    private string $clientSecret;
    private string $conta;
    private string $cooperativa;
    private string $documento;
    private string $identificadorPagamentoAssociado;
    private bool $mockMode;

    public function __construct()
    {
        $this->mockMode = env('API_PIX_MOCK_MODE', false);

        if (!$this->mockMode) {
            $this->initializeConfiguration();
        }
    }

    private function initializeConfiguration(): void
    {
        $this->certPath = env('API_SICREDI_CERT_PATH');
        $this->keyPath = env('API_SICREDI_KEY_PATH');
        $this->clientId = env('API_SICREDI_CLIENT_ID');
        $this->clientSecret = env('API_SICREDI_CLIENT_SECRET');
        $this->conta = env('API_SICREDI_CONTA');
        $this->cooperativa = env('API_SICREDI_COOPERATIVA');
        $this->documento = env('API_SICREDI_DOCUMENTO');

        if (!empty($this->certPath) && !str_starts_with($this->certPath, '/')) {
            $this->certPath = base_path($this->certPath);
        }

        if (!empty($this->keyPath) && !str_starts_with($this->keyPath, '/')) {
            $this->keyPath = base_path($this->keyPath);
        }

        $requiredConfigs = [
            'API_SICREDI_CERT_PATH' => $this->certPath,
            'API_SICREDI_KEY_PATH' => $this->keyPath,
            'API_SICREDI_CLIENT_ID' => $this->clientId,
            'API_SICREDI_CLIENT_SECRET' => $this->clientSecret,
            'API_SICREDI_CONTA' => $this->conta,
            'API_SICREDI_COOPERATIVA' => $this->cooperativa,
            'API_SICREDI_DOCUMENTO' => $this->documento,
        ];

        foreach ($requiredConfigs as $config => $value) {
            if (empty($value)) {
                throw new \RuntimeException("Configuração {$config} não encontrada no .env");
            }
        }
    }

    private function getHttpClient()
    {
        return Http::withOptions([
            'verify' => false,
            'cert' => $this->certPath,
            'ssl_key' => $this->keyPath,
            'timeout' => 30,
            'connect_timeout' => 10,
        ]);
    }

    public function getToken(): string
    {
        if ($this->mockMode) {
            return $this->getMockToken();
        }

        try {
            Log::info('ApiPixAdapter: Iniciando geração de token');

            $response = $this->getHttpClient()->withHeaders(
                [
                    'Authorization' => 'Basic ' . base64_encode($this->clientId . ':' . $this->clientSecret),
                    'Content-Type' => 'application/json',
                    'Accept' => '*/*',
                ]
            )->post(env('API_SICREDI_RECEBIMENTOS_BASE_URL') . '/oauth/token?grant_type=client_credentials&scope=' . self::API_PIX_SCOPE);

            if (!$response->successful()) {
                throw new ConnectionException('Erro ao gerar token: ' . $response->body());
            }

            $data = $response->json();
            if (!isset($data['access_token'])) {
                throw new ConnectionException('Token de acesso não encontrado na resposta');
            }

            $this->accessToken = $data['access_token'];
            Log::info('ApiPixAdapter: Token gerado com sucesso');

            return $this->accessToken;
        } catch (\Exception $e) {
            Log::error('ApiPixAdapter: Erro ao gerar token', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    private function getMockToken(): string
    {
        Log::info('ApiPixAdapter: Gerando token mocado (modo teste)');

        $this->accessToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzY29wZSI6WyJjb2IucmVhZCIsImNvYi53cml0ZSIsIndlYmhvb2sucmVhZCIsIndlYmhvb2sud3JpdGUiXSwiZXhwIjoxNzUxMDMwODI4LCJhdXRob3JpdGllcyI6WyJST0xFX0NMSUVOVCJdLCJqdGkiOiJfSXpDckppTWVqcjNxYkl3Q0hiT2drTXp5ZVkiLCJjbGllbnRfaWQiOiJNVGswTVRnNU9UY3dNREF4TWpJNk1EQXdNVHB3TTFFIn0._1Lq4jWmZodi_qwTTsksiuBDjSN2KDngYUv24QSK-6k";

        Log::info('ApiPixAdapter: Token mocado gerado com sucesso');

        return $this->accessToken;
    }

    public function createPixDeposit($dataPayment)
    {
        if ($this->mockMode) {
            return $this->getMockPixDeposit($dataPayment);
        }

        try {
            if (empty($this->accessToken)) {
                $this->getToken();
            }

            Log::info('ApiPixAdapter: Criando depósito PIX');

            $response = $this->getHttpClient()
                ->withToken($this->accessToken)
                ->withHeaders(['Content-Type' => 'application/json'])
                ->post(env('API_SICREDI_RECEBIMENTOS_BASE_URL') . '/api/v2/cob', $dataPayment);

            if (!$response->successful()) {
                $errorMessage = 'Erro ao criar depósito PIX: ' . $response->body();
                Log::error('ApiPixAdapter: ' . $errorMessage, [
                    'status_code' => $response->status(),
                    'response_body' => $response->body()
                ]);
                throw new ConnectionException($errorMessage);
            }

            $data = $response->json();
            Log::info('ApiPixAdapter: Depósito PIX criado com sucesso', ['response' => $data]);

            return $data;
        } catch (\Exception $e) {
            Log::error('ApiPixAdapter: Erro ao criar depósito PIX', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    private function getMockPixDeposit($dataPayment): array
    {
        Log::info('ApiPixAdapter: Criando depósito PIX mocado (modo teste)', ['payload' => $dataPayment]);

        // Simula um pequeno delay como se fosse uma chamada real
        usleep(500000); // 500ms

        $txid = Str::replace('-', '', Str::uuid()->toString());
        $locationId = rand(1000000000, 9999999999);
        $locationHash = Str::random(32);

        $mockResponse = [
            "calendario" => [
                "criacao" => now()->toISOString(),
                "expiracao" => 3600
            ],
            "status" => "ATIVA",
            "txid" => $txid,
            "revisao" => 0,
            "location" => "pix-qrcode.sicredi.com.br/qr/v2/{$locationHash}",
            "devedor" => [
                "cnpj" => $dataPayment['devedor']['cnpj'] ?? "12345678000195",
                "nome" => $dataPayment['devedor']['nome'] ?? "Empresa de Serviços SA"
            ],
            "loc" => [
                "id" => $locationId,
                "location" => "pix-qrcode.sicredi.com.br/qr/v2/{$locationHash}",
                "tipoCob" => "cob",
                "criacao" => now()->toISOString()
            ],
            "valor" => [
                "original" => $dataPayment['valor']['original'] ?? "37.00",
                "modalidadeAlteracao" => $dataPayment['valor']['modalidadeAlteracao'] ?? 1,
                "retirada" => null
            ],
            "chave" => $dataPayment['chave'] ?? "68b787ee-b3d4-4c49-a23d-cb6463deb10a",
            "solicitacaoPagador" => $dataPayment['solicitacaoPagador'] ?? "Serviço realizado.",
            "pixCopiaECola" => $this->generateMockPixCopyPaste($dataPayment['valor']['original'] ?? "37.00", $locationHash)
        ];

        Log::info('ApiPixAdapter: Depósito PIX mocado criado com sucesso', ['response' => $mockResponse]);

        return $mockResponse;
    }

    private function generateMockPixCopyPaste(string $valor, string $locationHash): string
    {
        // Gera um PIX Copia e Cola mock baseado no padrão real
        $basePixCode = "00020126860014br.gov.bcb.pix2564pix-qrcode.sicredi.com.br/qr/v2/{$locationHash}5204000053039865802BR5903PIX6006Cidade62070503***6304";

        // Adiciona um checksum mock (normalmente seria calculado)
        $checksum = strtoupper(substr(md5($basePixCode . $valor), 0, 4));

        return $basePixCode . $checksum;
    }

    public function getPixTransaction(string $transactionId): array
    {
        if ($this->mockMode) {
            return $this->getMockPixTransaction($transactionId);
        }

        try {
            if (empty($this->accessToken)) {
                $this->getToken();
            }

            Log::info('ApiPixAdapter: Consultando status da transação PIX', ['transaction_id' => $transactionId]);

            $response = $this->getHttpClient()
                ->withToken($this->accessToken)
                ->withHeaders(['Content-Type' => 'application/json', 'Accept' => '*/*'])
                ->get(env('API_SICREDI_RECEBIMENTOS_BASE_URL') . '/api/v2/cob/' . $transactionId);

            if (!$response->successful()) {
                $errorMessage = 'Erro ao consultar status da transação PIX: ' . $response->body();
                Log::error('ApiPixAdapter: ' . $errorMessage, [
                    'status_code' => $response->status(),
                    'response_body' => $response->body()
                ]);
                throw new ConnectionException($errorMessage);
            }

            $data = $response->json();
            Log::info('ApiPixAdapter: Status da transação PIX consultado com sucesso', ['response' => $data]);

            return $data;
        } catch (\Exception $e) {
            Log::error('ApiPixAdapter: Erro ao consultar status da transação PIX', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    private function getMockPixTransaction(string $transactionId): array
    {
        Log::info('ApiPixAdapter: Consultando transação PIX mocada (modo teste)', ['transaction_id' => $transactionId]);

        // Simula diferentes status baseado no ID da transação para testes
        $statuses = ['ATIVA', 'CONCLUIDA', 'REMOVIDA_PELO_USUARIO_RECEBEDOR'];
        $status = $statuses[crc32($transactionId) % count($statuses)];

        $mockResponse = [
            "calendario" => [
                "criacao" => now()->subMinutes(30)->toISOString(),
                "expiracao" => 3600
            ],
            "status" => $status,
            "txid" => $transactionId,
            "revisao" => 0,
            "location" => "pix-qrcode.sicredi.com.br/qr/v2/" . Str::random(32),
            "devedor" => [
                "cnpj" => "12345678000195",
                "nome" => "Empresa de Serviços SA"
            ],
            "valor" => [
                "original" => "37.00",
                "modalidadeAlteracao" => 1
            ],
            "chave" => "68b787ee-b3d4-4c49-a23d-cb6463deb10a",
            "solicitacaoPagador" => "Serviço realizado."
        ];

        // Se o status for CONCLUIDA, adiciona informações de pagamento
        if ($status === 'CONCLUIDA') {
            $mockResponse['pix'] = [
                [
                    "endToEndId" => "E" . date('Ymd') . Str::random(26),
                    "txid" => $transactionId,
                    "valor" => "37.00",
                    "horario" => now()->subMinutes(5)->toISOString(),
                    "infoPagador" => "Pagamento realizado com sucesso"
                ]
            ];
        }

        Log::info('ApiPixAdapter: Transação PIX mocada consultada com sucesso', ['response' => $mockResponse]);

        return $mockResponse;
    }

    public function isTokenValid(): bool
    {
        return !empty($this->accessToken);
    }

    public function clearToken(): void
    {
        $this->accessToken = '';
    }

    public function isMockMode(): bool
    {
        return $this->mockMode;
    }

    public function setMockMode(bool $mockMode): void
    {
        $this->mockMode = $mockMode;
    }
}
