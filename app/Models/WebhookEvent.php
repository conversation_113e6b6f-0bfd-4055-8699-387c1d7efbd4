<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WebhookEvent extends Model
{
    protected $fillable = [
        'user_id',
        'pix_transaction_id',
        'event_type',
        'payload',
        'status',
        'attempts',
        'error_message',
        'last_attempt_at',
        'delivered_at',
    ];

    protected $casts = [
        'payload' => 'array',
        'last_attempt_at' => 'datetime',
        'delivered_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function pixTransaction(): BelongsTo
    {
        return $this->belongsTo(PixTransaction::class);
    }

    /**
     * Incrementa o número de tentativas
     */
    public function incrementAttempts(): void
    {
        $this->increment('attempts');
        $this->update(['last_attempt_at' => now()]);
    }

    /**
     * Marca como entregue
     */
    public function markAsDelivered(): void
    {
        $this->update([
            'status' => 'delivered',
            'delivered_at' => now(),
            'error_message' => null
        ]);
    }

    /**
     * Marca como falha
     */
    public function markAsFailed( $errorMessage = null): void
    {
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
            'last_attempt_at' => now()
        ]);
    }

    /**
     * Verifica se deve tentar novamente
     */
    public function shouldRetry(): bool
    {
        return $this->attempts < 3 && $this->status !== 'delivered';
    }

    /**
     * Scope para webhooks pendentes
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope para webhooks que falharam
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope para webhooks entregues
     */
    public function scopeDelivered($query)
    {
        return $query->where('status', 'delivered');
    }
}
