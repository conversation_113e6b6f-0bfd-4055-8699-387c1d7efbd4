<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PixAccount extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'bank_code',
        'account_number',
        'account_type',
        'is_active',
        'balance',
        'pix_key',
        'pix_key_type',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function pixTransactions()
    {
        return $this->hasMany(PixTransaction::class);
    }
}
