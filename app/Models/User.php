<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\SoftDeletes;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes;

    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'email_verified_at',
        'role',
        'password',
        'phone',
        'document',
        'document_type',
        'status',
        'webhook_url',
        'agreement_terms',
        'kyc_status',
        'document_front_url',
        'document_back_url',
        'selfie_url',
        'kyc_verified_at',
        'kyc_rejection_reason',
        'document_company',
        'company_legal_name',
        'company_trading_name',
        'company_contract_url',
        'company_statute_url',
        'company_proxy_url'
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'kyc_verified_at' => 'datetime',
        'agreement_terms' => 'boolean'
    ];

    public function apiKeys()
    {
        return $this->hasMany(ApiKey::class);
    }

    public function pixAccounts()
    {
        return $this->hasMany(PixAccount::class);
    }

    public function isKycVerified(): bool
    {
        return $this->kyc_status === 'verified' && $this->kyc_verified_at !== null;
    }

    public function isKycPending(): bool
    {
        return $this->kyc_status === 'pending';
    }

    public function isKycRejected(): bool
    {
        return $this->kyc_status === 'rejected';
    }

    public function isPJ(): bool
    {
        return $this->document_type === 'cnpj';
    }

    public function isPF(): bool
    {
        return $this->document_type === 'cpf';
    }

    public function hasRequiredPJDocuments(): bool
    {
        if (!$this->isPJ()) {
            return true;
        }

        return !empty($this->company_contract_url) &&
               !empty($this->company_statute_url) &&
               !empty($this->company_proxy_url);
    }
}
