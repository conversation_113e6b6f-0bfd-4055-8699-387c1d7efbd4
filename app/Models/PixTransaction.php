<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PixTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'pix_account_id',
        'pix_transaction_id',
        'order_id',
        'transaction_id_external',
        'type',
        'amount',
        'status',
        'description',
        'pix_key',
        'qr_code',
        'document_beneficiary',
        'external_transaction_id',
        'processed_at',
    ];

    protected $casts = [
        'processed_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function webhookEvents()
    {
        return $this->hasMany(WebhookEvent::class);
    }
}
