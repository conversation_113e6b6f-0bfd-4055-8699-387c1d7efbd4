<?php

namespace App\Services;

use App\Models\PixAccount;
use App\Models\User;
use App\Traits\MonetaryConversion;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Collection;
use Ramsey\Uuid\Type\Decimal;

class PixAccountService
{
    use MonetaryConversion;

    public function create(User $user): PixAccount
    {
        DB::beginTransaction();
        try {
            $pixAccount = PixAccount::create([
                'user_id' => $user->id,
                'bank_code' => '000001',
                'account_number' => (string) ($user->id . time()),
                'account_type' => 'pagamento',
                'is_active' => true,
                'balance' => 0,
                'pix_key' => $user->document,
                'pix_key_type' => $user->document_type,
            ]);
            DB::commit();
            return $pixAccount;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function update(PixAccount $account): bool
    {
        return $account->update([
            'is_active' => true,
        ]);
    }

    public function deactivate(PixAccount $account): bool
    {
        return $account->update(['is_active' => false]);
    }

    /**
     * @return Collection<int, PixAccount>
     */
    public function list(User $user): Collection
    {
        return $user->pixAccounts()->get();
    }

    public function findByPixAccountId(User $user)
    {
        return $user->pixAccounts()->where('user_id', $user->id)->first();
    }

    public function getBalance(PixAccount $account)
    {
        return $this->convertToReais($account->balance);
    }

    public function createWithType(User $user, string $pixKeyType = 'cpf'): PixAccount
    {
        DB::beginTransaction();
        try {
            $pixAccount = PixAccount::create([
                'user_id' => $user->id,
                'bank_code' => '000001',
                'account_number' => (string) ($user->id . time()),
                'account_type' => 'pagamento',
                'is_active' => true,
                'balance' => 0,
                'pix_key' => $user->document,
                'pix_key_type' => $pixKeyType,
            ]);
            DB::commit();
            return $pixAccount;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function createWebhook(User $user, array $data)
    {
        return $user->update([
            'webhook_url' => $data['webhook_url'],
        ]);
    }
}
