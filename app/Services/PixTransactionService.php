<?php

namespace App\Services;

use App\Adapters\MultPagAdapter;
use App\Adapters\ApiPixAdapter;
use App\Models\PixTransaction;
use App\Models\PixAccount;
use App\Models\User;
use App\Traits\MonetaryConversion;
use App\Http\Resources\PixTransactionWithdrawResource;
use App\Http\Resources\PixTransactionDepositResource;
use App\Jobs\ProcessPixDeposit;
use App\Jobs\ProcessPixWithdraw;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class PixTransactionService
{
    use MonetaryConversion;

    public function __construct(
        private WebhookService $webhookService
    ) {}

    public function create(User $user, PixAccount $account, array $data): PixTransaction
    {
        return PixTransaction::create([
            'user_id' => $user->id,
            'pix_account_id' => $account->id,
            'pix_transaction_id' => Str::uuid(),
            'order_id' => Str::uuid() . "-" . $user->id,
            'transaction_id_external' => Str::uuid() . "-" . $user->id,
            'type' => $data['type'],
            'amount' => $data['amount'],
            'status' => 'pending',
            'description' => $data['description'] ?? null,
            'pix_key' => $data['pix_key'],
            'qr_code' => null, // Será preenchido pelo gateway
            'document_beneficiary' => $data['document_beneficiary'],
        ]);
    }

    public function findByExternalId(string $externalId): ?PixTransaction
    {
        return PixTransaction::where('transaction_id_external', $externalId)->first();
    }

    public function createDeposit(User $user, PixAccount $account, array $data)
    {
        DB::beginTransaction();
        try {
            $amountInCents = $this->convertToCents($data['amount']);

            $transaction = $this->create($user, $account, [
                'type' => 'deposit',
                'amount' => $amountInCents,
                'description' => $data['description'] ?? "Depósito PIX",
                'pix_key' => $account->pix_key,
                'document_beneficiary' => $data['document_beneficiary'],
                'name' => $data['name']
            ]);

            $pixPayload = [
                "calendario" => [
                    "expiracao" => 7200
                ],
                "devedor" => [
                    "cnpj" => $data['document_beneficiary'],
                    "nome" => $data['name']
                ],
                "valor" => [
                    "original" => $data['amount'],
                    "modalidadeAlteracao" => 1
                ],
                "chave" => env('PIX_KEY_ASSOCIADO'),
                "solicitacaoPagador" => "Depósito PIX",
            ];

            if (isset($data['infoAdicionais'])) {
                $pixPayload['infoAdicionais'] = $data['infoAdicionais'];
            }

            // Dispatcha o job para processar o depósito de forma assíncrona
            ProcessPixDeposit::dispatch($transaction, $pixPayload);

            DB::commit();

            // Retorna a transação com status pending enquanto processa
            return new PixTransactionDepositResource($transaction);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function createWithdraw(User $user, PixAccount $account, $data)
    {
        DB::beginTransaction();
        try {
            $amountInCents = $this->convertToCents($data['amount']);

            if ($account->balance < $amountInCents) {
                throw new \Exception('Saldo insuficiente para realizar o saque');
            }

            $transaction = $this->create($user, $account, [
                'type' => 'withdraw',
                'amount' => $amountInCents,
                'description' => $data['description'] ?? "Saque PIX",
                'pix_key' => $data['pix_key'],
                'document_beneficiary' => $data['document_beneficiary']
            ]);

            // Dispatcha o job para processar o saque de forma assíncrona
            ProcessPixWithdraw::dispatch($transaction);

            DB::commit();

            // Retorna a transação com status pending enquanto processa
            return new PixTransactionWithdrawResource($transaction);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function list(User $user, array $filters = [])
    {
        $query = PixTransaction::where('user_id', $user->id);

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        return $query->orderBy('created_at', 'desc')->paginate(20);
    }

    public function find(User $user, string $id): ?PixTransaction
    {
        return PixTransaction::where('user_id', $user->id)
            ->where('id', $id)
            ->first();
    }
}
