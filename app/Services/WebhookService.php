<?php

namespace App\Services;

use App\Jobs\ProcessWebhookEvent;
use App\Models\PixTransaction;
use App\Models\User;
use App\Models\WebhookEvent;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class WebhookService
{
    public function dispatchTransactionEvent(PixTransaction $transaction, string $eventType): void
    {

        DB::beginTransaction();
        try {
            $webhookEvent = WebhookEvent::create([
                'user_id' => $transaction->user_id,
                'pix_transaction_id' => $transaction->id,
                'event_type' => $eventType,
                'status' => 'pending',
                'payload' => [
                    'transaction_id' => $transaction->transaction_id_external,
                    'type' => $transaction->type,
                    'amount' => $transaction->amount,
                    'status' => $transaction->status,
                    'description' => $transaction->description,
                    'pix_key' => $transaction->pix_key,
                    'created_at' => $transaction->created_at->toIso8601String(),
                ]
            ]);

            ProcessWebhookEvent::dispatch($webhookEvent);

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function dispatchTransactionCreated(PixTransaction $transaction): void
    {
        $this->createAndDispatchWebhook(
            $transaction,
            'transaction.created',
            [
                'transaction_id' => $transaction->transaction_id_external,
                'order_id' => $transaction->order_id,
                'type' => $transaction->type,
                'amount' => $transaction->amount,
                'status' => $transaction->status,
                'qr_code' => $transaction->qr_code,
                'created_at' => $transaction->created_at->toIso8601String()
            ]
        );
    }

    public function dispatchTransactionUpdated(PixTransaction $transaction): void
    {
        $this->dispatchTransactionEvent($transaction, 'transaction.updated');
    }

    public function dispatchTransactionCompleted(PixTransaction $transaction): void
    {
        $this->createAndDispatchWebhook(
            $transaction,
            'transaction.completed',
            [
                'transaction_id' => $transaction->transaction_id_external,
                'order_id' => $transaction->order_id,
                'type' => $transaction->type,
                'amount' => $transaction->amount,
                'status' => $transaction->status,
                'completed_at' => now()->toIso8601String()
            ]
        );
    }

    public function dispatchTransactionFailed(PixTransaction $transaction): void
    {
        $this->createAndDispatchWebhook(
            $transaction,
            'transaction.failed',
            [
                'transaction_id' => $transaction->transaction_id_external,
                'order_id' => $transaction->order_id,
                'type' => $transaction->type,
                'amount' => $transaction->amount,
                'status' => $transaction->status,
                'failed_at' => now()->toIso8601String()
            ]
        );
    }

    public function dispatchKycStatusChanged(User $user, string $status): void
    {
        if (!$user->webhook_url) {
            Log::info("Usuário {$user->id} não possui webhook configurado");
            return;
        }

        $webhookEvent = WebhookEvent::create([
            'event_type' => 'kyc.status_changed',
            'payload' => json_encode([
                'user_id' => $user->id,
                'kyc_status' => $status,
                'document_type' => $user->document_type,
                'changed_at' => now()->toIso8601String()
            ]),
            'status' => 'pending',
            'user_id' => $user->id,
            'pix_transaction_id' => null
        ]);

        ProcessWebhookEvent::dispatch($webhookEvent)->onQueue('webhooks');
    }

    private function createAndDispatchWebhook(PixTransaction $transaction, string $eventType, array $payload): void
    {
        $user = $transaction->user;

        if (!$user->webhook_url) {
            Log::info("Usuário {$user->id} não possui webhook configurado");
            return;
        }

        $webhookEvent = WebhookEvent::create([
            'event_type' => $eventType,
            'payload' => json_encode($payload),
            'status' => 'pending',
            'user_id' => $user->id,
            'pix_transaction_id' => $transaction->id
        ]);

        ProcessWebhookEvent::dispatch($webhookEvent)->onQueue('webhooks');
    }

    public function processWebhook(array $payload): bool
    {
        try {
            Log::info('Processando webhook recebido', ['payload' => $payload]);

            // Aqui você pode adicionar lógica para processar webhooks recebidos
            // Por exemplo, atualizar status de transações baseado em notificações externas

            return true;
        } catch (\Exception $e) {
            Log::error('Erro ao processar webhook', [
                'error' => $e->getMessage(),
                'payload' => $payload
            ]);
            return false;
        }
    }
}
