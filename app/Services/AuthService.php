<?php

namespace App\Services;

use App\Models\User;
use App\Jobs\ProcessKycDocuments;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class AuthService
{
    public function __construct(
        private User $user,
        private ApiKeyService $apiKeyService,
        private KycService $kycService,
        private PixAccountService $pixAccountService
    ) {}

    public function registerPixAccount(array $data): array
    {
        DB::beginTransaction();
        try {
            $documentType = $this->determineDocumentType($data['document']);
            $password = isset($data['password']) ? Hash::make($data['password']) : Hash::make('abcd@1234');
            $isCreatedByApi = isset($data['agreement_terms']) ?  $data['agreement_terms'] === true : false;

            if ($documentType === 'cnpj') {
                $this->validatePJData($data);
            } else {
                $this->validatePFData($data);
            }

            $user = User::create([
                'first_name' => $data['first_name'],
                'last_name' => $data['last_name'],
                'email' => $data['email'],
                'password' => $password,
                'phone' => $data['phone'],
                'document' => $data['document'],
                'document_type' => $documentType,
                'agreement_terms' => $isCreatedByApi,
                'kyc_status' => 'pending',
                'status' => 'active',
                // Campos PJ se aplicável
                'document_company' => $data['document_company'] ?? null,
                'company_legal_name' => $data['company_legal_name'] ?? null,
                'company_trading_name' => $data['company_trading_name'] ?? null,
            ]);

            // Preparar documentos para processamento assíncrono
            // Converte UploadedFiles para paths temporários para evitar erro de serialização
            $documents = [];

            $documentFields = ['document_front', 'document_back', 'selfie'];
            if ($documentType === 'cnpj') {
                $documentFields = array_merge($documentFields, [
                    'company_contract', 'company_statute', 'company_proxy'
                ]);
            }

            foreach ($documentFields as $field) {
                if (isset($data[$field]) && $data[$field]) {
                    // Salva o arquivo temporariamente e guarda o path
                    $tempPath = $data[$field]->store('temp/kyc', 'local');
                    $documents[$field] = [
                        'temp_path' => $tempPath,
                        'original_name' => $data[$field]->getClientOriginalName(),
                        'mime_type' => $data[$field]->getMimeType(),
                        'size' => $data[$field]->getSize()
                    ];
                }
            }

            // Dispatcha job para processar documentos KYC de forma assíncrona
            ProcessKycDocuments::dispatch($user, $documents);

            // Criação da api_key usando o ApiKeyService
            $apiKey = $this->apiKeyService->create($user);
            if (!$apiKey) throw new \Exception('Erro ao criar api key.');

            // Criação da conta Pix (usando o tipo de documento apropriado)
            $pixAccount = $this->pixAccountService->createWithType($user, $documentType);
            if (!$pixAccount) throw new \Exception('Erro ao criar conta pix.');

            DB::commit();

            return [
                'user' => $user,
                'credentials' => $apiKey,
                'kyc_status' => 'pending',
                'message' => 'Usuário registrado com sucesso. Documentos KYC sendo processados.'
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    private function validatePFData(array $data): void
    {
        $requiredFields = ['document_front', 'document_back', 'selfie'];

        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || !$data[$field]) {
                throw new \Exception("Campo {$field} é obrigatório para pessoas físicas");
            }
        }

        if (!$this->isValidCPF($data['document'])) {
            throw new \Exception('CPF inválido');
        }
    }

    private function validatePJData(array $data): void
    {
        $requiredFields = [
            'document_front',
            'document_back',
            'selfie',
            'document_company',
            'company_legal_name',
            'company_trading_name',
            'company_contract',
            'company_statute',
            'company_proxy'
        ];

        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || !$data[$field]) {
                throw new \Exception("Campo {$field} é obrigatório para pessoas jurídicas");
            }
        }

        if (!$this->isValidCNPJ($data['document'])) {
            throw new \Exception('CNPJ inválido');
        }

        if (!$this->isValidCPF($data['document_company'])) {
            throw new \Exception('CPF do representante legal inválido');
        }
    }

    private function determineDocumentType(string $document): string
    {
        $cleanDocument = preg_replace('/[^0-9]/', '', $document);
        return strlen($cleanDocument) === 14 ? 'cnpj' : 'cpf';
    }

    private function isValidCPF(string $cpf): bool
    {
        // Remove caracteres não numéricos
        $cpf = preg_replace('/[^0-9]/', '', $cpf);

        // Verifica se tem 11 dígitos
        if (strlen($cpf) != 11) {
            return false;
        }

        // Verifica se todos os dígitos são iguais
        if (preg_match('/(\d)\1{10}/', $cpf)) {
            return false;
        }

        // Validação do primeiro dígito verificador
        $sum = 0;
        for ($i = 0; $i < 9; $i++) {
            $sum += $cpf[$i] * (10 - $i);
        }
        $rest = $sum % 11;
        $digit1 = $rest < 2 ? 0 : 11 - $rest;

        if ($cpf[9] != $digit1) {
            return false;
        }

        // Validação do segundo dígito verificador
        $sum = 0;
        for ($i = 0; $i < 10; $i++) {
            $sum += $cpf[$i] * (11 - $i);
        }
        $rest = $sum % 11;
        $digit2 = $rest < 2 ? 0 : 11 - $rest;

        return $cpf[10] == $digit2;
    }

    private function isValidCNPJ(string $cnpj): bool
    {
        // Remove caracteres não numéricos
        $cnpj = preg_replace('/[^0-9]/', '', $cnpj);

        // Verifica se tem 14 dígitos
        if (strlen($cnpj) != 14) {
            return false;
        }

        // Verifica se todos os dígitos são iguais
        if (preg_match('/(\d)\1{13}/', $cnpj)) {
            return false;
        }

        // Validação do primeiro dígito verificador
        $sum = 0;
        $weight = [5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];

        for ($i = 0; $i < 12; $i++) {
            $sum += $cnpj[$i] * $weight[$i];
        }

        $rest = $sum % 11;
        $digit1 = $rest < 2 ? 0 : 11 - $rest;

        if ($cnpj[12] != $digit1) {
            return false;
        }

        // Validação do segundo dígito verificador
        $sum = 0;
        $weight = [6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];

        for ($i = 0; $i < 13; $i++) {
            $sum += $cnpj[$i] * $weight[$i];
        }

        $rest = $sum % 11;
        $digit2 = $rest < 2 ? 0 : 11 - $rest;

        return $cnpj[13] == $digit2;
    }

    public function login(array $credentials): array
    {
        if (!Auth::attempt($credentials)) {
            throw ValidationException::withMessages([
                'email' => ['Credenciais inválidas.'],
            ]);
        }

        $user = $this->user->where('email', $credentials['email'])->first();
        $user->tokens()->delete();

        $token = $user->createToken('access_token')->plainTextToken;

        return [
            'user' => $user,
            'access_token' => $token,
            'token_type' => 'Bearer',
        ];
    }

    public function logout(Request $request): bool
    {
        return $request->user()->currentAccessToken()->delete();
    }

    public function me(Request $request): User
    {
        return $request->user()->load(['apiKeys', 'pixAccounts']);
    }
}

