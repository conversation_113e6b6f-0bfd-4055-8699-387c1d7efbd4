<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\File;

class KycService
{
    private const ALLOWED_MIME_TYPES = [
        'image/jpeg',
        'image/png',
        'application/pdf'
    ];

    private const MAX_FILE_SIZE = 5242880; // 5MB

    private const PJ_DOCUMENT_TYPES = [
        'company_contract',
        'company_statute',
        'company_proxy',
        'document_front',
        'document_back',
        'selfie'
    ];

    private const PF_DOCUMENT_TYPES = [
        'document_front',
        'document_back',
        'selfie'
    ];

    public function uploadDocument(User $user, UploadedFile $file, string $type): string
    {
        $this->validateFile($file);
        $this->validateDocumentType($user, $type);

        $path = $this->generatePath($user, $file, $type);

        // Implementação S3 (comentada temporariamente)
        /*
        $uploaded = Storage::disk('s3')->put($path, file_get_contents($file), [
            'visibility' => 'private',
            'ContentType' => $file->getMimeType()
        ]);

        if (!$uploaded) {
            throw new \Exception('Falha ao fazer upload do documento');
        }

        return Storage::disk('s3')->url($path);
        */

        // Implementação Local
        $uploaded = Storage::disk('local')->put($path, file_get_contents($file), [
            'visibility' => 'private',
            'ContentType' => $file->getMimeType()
        ]);

        if (!$uploaded) {
            throw new \Exception('Falha ao fazer upload do documento');
        }

        return asset('storage/' . $path);
    }

    public function uploadDocumentFromTempPath(User $user, string $tempPath, string $originalName, string $mimeType, string $type): string
    {
        // Verifica se o arquivo temporário existe
        if (!Storage::disk('local')->exists($tempPath)) {
            throw new \Exception('Arquivo temporário não encontrado: ' . $tempPath);
        }

        $this->validateDocumentType($user, $type);

        // Gera o path final do documento
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        $filename = $type . '_' . Str::uuid() . '.' . $extension;
        $hash = hash('xxh3', $user->id . config('app.key'));
        $finalPath = sprintf(
            'public/kyc/%s_%s/%s',
            $hash,
            $user->id,
            $filename
        );

        // Copia o arquivo da localização temporária para a final
        $fileContents = Storage::disk('local')->get($tempPath);

        $uploaded = Storage::disk('local')->put($finalPath, $fileContents, [
            'visibility' => 'private',
            'ContentType' => $mimeType
        ]);

        if (!$uploaded) {
            throw new \Exception('Falha ao fazer upload do documento');
        }

        // Remove o arquivo temporário
        Storage::disk('local')->delete($tempPath);

        return asset('storage/' . $finalPath);
    }

    public function updateKycStatus(User $user, string $status, ?string $rejectionReason = null): bool
    {
        $data = [
            'kyc_status' => $status
        ];

        if ($status === 'verified') {
            $data['kyc_verified_at'] = now();
            $data['kyc_rejection_reason'] = null;
        } elseif ($status === 'rejected') {
            $data['kyc_rejection_reason'] = $rejectionReason;
        }

        return $user->update($data);
    }

    private function validateFile(UploadedFile $file): void
    {
        if (!in_array($file->getMimeType(), self::ALLOWED_MIME_TYPES)) {
            throw new \Exception('Tipo de arquivo não permitido. Use apenas JPG, PNG ou PDF.');
        }

        if ($file->getSize() > self::MAX_FILE_SIZE) {
            throw new \Exception('Arquivo muito grande. O tamanho máximo permitido é 5MB.');
        }
    }

    private function generatePath(User $user, UploadedFile $file, string $type): string
    {
        $extension = $file->getClientOriginalExtension();
        $filename = $type . '_' . Str::uuid() . '.' . $extension;
        // Gera um hash único baseado no ID do usuário
        $hash = hash('xxh3', $user->id . config('app.key'));

        return sprintf(
            'public/kyc/%s_%s/%s',
            $hash,
            $user->id,
            $filename
        );
    }

    private function validateDocumentType(User $user, string $type): void
    {
        $allowedTypes = $user->isPJ() ? self::PJ_DOCUMENT_TYPES : self::PF_DOCUMENT_TYPES;

        if (!in_array($type, $allowedTypes)) {
            throw new \Exception(
                $user->isPJ()
                    ? 'Tipo de documento inválido para PJ. Use apenas contrato social, estatuto ou procuração.'
                    : 'Tipo de documento inválido para PF. Use apenas frente do documento, verso do documento ou selfie.'
            );
        }
    }

    public function getRequiredDocuments(User $user): array
    {
        if ($user->isPJ()) {
            return [
                'company_contract' => 'Contrato Social',
                'company_statute' => 'Estatuto Social',
                'company_proxy' => 'Procuração',
                'document_front' => 'CNPJ',
                'document_back' => 'CNPJ',
                'selfie' => 'Selfie do Responsável Legal'
            ];
        }

        return [
            'document_front' => 'Frente do Documento',
            'document_back' => 'Verso do Documento',
            'selfie' => 'Selfie'
        ];
    }
}
