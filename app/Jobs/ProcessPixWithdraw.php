<?php

namespace App\Jobs;

use App\Models\PixTransaction;
use App\Adapters\MultPagAdapter;
use App\Services\WebhookService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProcessPixWithdraw implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;
    public $timeout = 120;

    public function __construct(
        private PixTransaction $transaction
    ) {
        $this->onQueue('pix-withdraws');
    }

    public function handle(
        MultPagAdapter $multPagAdapter,
        WebhookService $webhookService
    ): void {
        DB::beginTransaction();
        try {
            Log::info('Processando saque PIX', [
                'transaction_id' => $this->transaction->id,
                'order_id' => $this->transaction->order_id
            ]);

            $response = $multPagAdapter->withdrawPixKey($this->transaction);

            if ($response['status'] === 'RECEBIDO') {
                $this->transaction->update(['status' => 'received']);

                // Aguarda 3 segundos antes de verificar o status
                sleep(3);

                $checkStatusPixTransaction = $multPagAdapter->getPixTransaction($response['idTransacao']);

                if ($checkStatusPixTransaction['status'] === 'SUCESSO') {
                    $this->transaction->update(['status' => 'completed']);
                    $webhookService->dispatchTransactionCompleted($this->transaction);

                    Log::info('Saque PIX processado com sucesso', [
                        'transaction_id' => $this->transaction->id,
                        'status' => 'completed'
                    ]);
                } else {
                    $this->transaction->update(['status' => 'failed']);
                    $webhookService->dispatchTransactionFailed($this->transaction);

                    Log::error('Saque PIX falhou na verificação de status', [
                        'transaction_id' => $this->transaction->id,
                        'status_response' => $checkStatusPixTransaction
                    ]);
                }
            } else {
                $this->transaction->update(['status' => 'failed']);
                $webhookService->dispatchTransactionFailed($this->transaction);

                Log::error('Falha ao processar saque PIX', [
                    'transaction_id' => $this->transaction->id,
                    'response' => $response
                ]);
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Erro ao processar saque PIX', [
                'transaction_id' => $this->transaction->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $this->transaction->update(['status' => 'failed']);
            throw $e;
        }
    }

    public function failed(\Throwable $exception): void
    {
        Log::error('Job ProcessPixWithdraw falhou definitivamente', [
            'transaction_id' => $this->transaction->id,
            'error' => $exception->getMessage()
        ]);

        $this->transaction->update(['status' => 'failed']);
    }
}
