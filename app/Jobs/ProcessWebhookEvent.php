<?php

namespace App\Jobs;

use App\Models\WebhookEvent;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ProcessWebhookEvent implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;
    public $timeout = 30;

    public function __construct(
        private WebhookEvent $webhookEvent
    ) {
        $this->onQueue('webhooks');
    }

    public function handle(): void
    {
        DB::beginTransaction();
        try {
            $user = $this->webhookEvent->user;

            if (!$user->webhook_url) {
                Log::warning("Usuário {$user->id} não possui URL de webhook configurada");
                $this->webhookEvent->markAsFailed('Usuário não possui webhook configurado');
                DB::commit();
                return;
            }

            if ($this->webhookEvent->status === 'delivered') {
                Log::info("Webhook {$this->webhookEvent->id} já foi entregue");
                DB::commit();
                return;
            }

            $this->webhookEvent->incrementAttempts();

            $payload = [
                'event_type' => $this->webhookEvent->event_type,
                'payload' => $this->webhookEvent->payload,
                'timestamp' => now()->toIso8601String(),
                'webhook_id' => $this->webhookEvent->id,
            ];

            // Adiciona dados específicos da transação se existir
            if ($this->webhookEvent->pixTransaction) {
                $payload['transaction_id'] = $this->webhookEvent->pixTransaction->transaction_id_external;
                $payload['status'] = $this->webhookEvent->pixTransaction->status;
            }

            Log::info("Enviando webhook {$this->webhookEvent->id} para {$user->webhook_url}");

            $response = Http::timeout(10)
                ->retry(2, 100)
                ->post($user->webhook_url, $payload);

            if ($response->successful()) {
                $this->webhookEvent->markAsDelivered();
                Log::info("Webhook {$this->webhookEvent->id} entregue com sucesso");
            } else {
                $errorMessage = "HTTP {$response->status()}: {$response->body()}";

                if ($this->webhookEvent->shouldRetry()) {
                    Log::warning("Webhook {$this->webhookEvent->id} falhou, tentativa {$this->webhookEvent->attempts}/3", [
                        'error' => $errorMessage
                    ]);
                    throw new \Exception($errorMessage);
                } else {
                    $this->webhookEvent->markAsFailed($errorMessage);
                    Log::error("Webhook {$this->webhookEvent->id} falhou definitivamente após 3 tentativas", [
                        'error' => $errorMessage
                    ]);
                }
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();

            Log::error("Erro ao processar webhook {$this->webhookEvent->id}", [
                'error' => $e->getMessage(),
                'attempt' => $this->webhookEvent->attempts
            ]);

            if (!$this->webhookEvent->shouldRetry()) {
                $this->webhookEvent->markAsFailed($e->getMessage());
            }

            throw $e;
        }
    }

    public function failed(\Throwable $exception): void
    {
        Log::error("Job ProcessWebhookEvent falhou definitivamente", [
            'webhook_event_id' => $this->webhookEvent->id,
            'error' => $exception->getMessage()
        ]);

        $this->webhookEvent->markAsFailed('Job falhou: ' . $exception->getMessage());
    }
}
