<?php

namespace App\Jobs;

use App\Models\User;
use App\Services\KycService;
use App\Services\WebhookService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ProcessKycDocuments implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;
    public $timeout = 300;

    public function __construct(
        private User $user,
        private array $documents
    ) {
        $this->onQueue('kyc-processing');
    }

    public function handle(
        KycService $kycService,
        WebhookService $webhookService
    ): void {
        DB::beginTransaction();
        try {
            Log::info('Processando documentos KYC', [
                'user_id' => $this->user->id,
                'document_type' => $this->user->document_type
            ]);

            if ($this->user->document_type === 'cnpj') {
                $this->processPJDocuments($kycService);
            } else {
                $this->processPFDocuments($kycService);
            }

            $this->user->update([
                'kyc_status' => 'under_review'
            ]);

            $webhookService->dispatchKycStatusChanged($this->user, 'under_review');

            Log::info('Documentos KYC processados com sucesso', [
                'user_id' => $this->user->id,
                'kyc_status' => 'under_review'
            ]);

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Erro ao processar documentos KYC', [
                'user_id' => $this->user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $this->user->update(['kyc_status' => 'rejected']);
            throw $e;
        }
    }

    private function processPFDocuments(KycService $kycService): void
    {
        $documentUrls = [];

        $fields = ['document_front', 'document_back', 'selfie'];
        foreach ($fields as $field) {
            if (isset($this->documents[$field])) {
                $documentUrls[$field . '_url'] = $kycService->uploadDocumentFromTempPath(
                    $this->user,
                    $this->documents[$field]['temp_path'],
                    $this->documents[$field]['original_name'],
                    $this->documents[$field]['mime_type'],
                    $field
                );
            }
        }

        $this->user->update($documentUrls);
    }

    private function processPJDocuments(KycService $kycService): void
    {
        $documentUrls = [];

        $fields = ['document_front', 'document_back', 'selfie', 'company_contract', 'company_statute', 'company_proxy'];
        foreach ($fields as $field) {
            if (isset($this->documents[$field])) {
                $documentUrls[$field . '_url'] = $kycService->uploadDocumentFromTempPath(
                    $this->user,
                    $this->documents[$field]['temp_path'],
                    $this->documents[$field]['original_name'],
                    $this->documents[$field]['mime_type'],
                    $field
                );
            }
        }

        $this->user->update($documentUrls);
    }

    public function failed(\Throwable $exception): void
    {
        Log::error('Job ProcessKycDocuments falhou definitivamente', [
            'user_id' => $this->user->id,
            'error' => $exception->getMessage()
        ]);

        // Limpa arquivos temporários se existirem
        $this->cleanupTempFiles();

        $this->user->update([
            'kyc_status' => 'rejected',
            'kyc_rejection_reason' => 'Erro no processamento dos documentos: ' . $exception->getMessage()
        ]);
    }

    private function cleanupTempFiles(): void
    {
        foreach ($this->documents as $document) {
            if (isset($document['temp_path']) && Storage::disk('local')->exists($document['temp_path'])) {
                Storage::disk('local')->delete($document['temp_path']);
            }
        }
    }
}
