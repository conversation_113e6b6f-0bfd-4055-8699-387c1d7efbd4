<?php

namespace App\Jobs;

use App\Models\PixTransaction;
use App\Models\User;
use App\Models\PixAccount;
use App\Adapters\ApiPixAdapter;
use App\Services\WebhookService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProcessPixDeposit implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;
    public $timeout = 60;

    public function __construct(
        private PixTransaction $transaction,
        private array $pixPayload
    ) {
        $this->onQueue('pix-deposits');
    }

    public function handle(
        ApiPixAdapter $ApiPixAdapter,
        WebhookService $webhookService
    ): void {
        DB::beginTransaction();
        try {
            Log::info('Processando depósito PIX', [
                'transaction_id' => $this->transaction->id,
                'order_id' => $this->transaction->order_id
            ]);

            $response = $ApiPixAdapter->createPixDeposit($this->pixPayload);

            if (isset($response['status']) && $response['status'] === 'ATIVA') {
                $this->transaction->update([
                    'status' => 'pending_payment',
                    'qr_code' => $response['pixCopiaECola'] ?? $response['qrcode'] ?? null,
                ]);

                $webhookService->dispatchTransactionCreated($this->transaction);

                Log::info('Depósito PIX processado com sucesso', [
                    'transaction_id' => $this->transaction->id,
                    'status' => 'pending_payment'
                ]);
            } else {
                $this->transaction->update(['status' => 'failed']);
                $webhookService->dispatchTransactionFailed($this->transaction);

                Log::error('Falha ao criar cobrança PIX no gateway', [
                    'transaction_id' => $this->transaction->id,
                    'response' => $response
                ]);
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Erro ao processar depósito PIX', [
                'transaction_id' => $this->transaction->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $this->transaction->update(['status' => 'failed']);
            throw $e;
        }
    }

    public function failed(\Throwable $exception): void
    {
        Log::error('Job ProcessPixDeposit falhou definitivamente', [
            'transaction_id' => $this->transaction->id,
            'error' => $exception->getMessage()
        ]);

        $this->transaction->update(['status' => 'failed']);
    }
}
